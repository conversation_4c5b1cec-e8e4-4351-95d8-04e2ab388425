"""
线性化校准
"""

import  numpy as np
import  matplotlib.pyplot as plt
from scipy.interpolate import interp1d

def getFunc(spd):
    """
    得到对应列表数据
    spdPath:
        光谱数据
    """
    # 读取最大通道数据
    spdMax = np.max(spd[11:433,:], 0)
    # 得到关于最大值的比例
    lightScale = spdMax / np.max(spdMax)

    # 节点值
    notes = spd[0,:]
    # 实际的电流值
    currentScale =notes/np.max(notes)

    # 补0
    currentScale = sorted(currentScale,reverse=True)
    lightScale = sorted(lightScale,reverse=True)
    if min(currentScale) > 0 :
        currentScale.append(0)
        lightScale.append(0)

    # 补1
    # currentScale.append(0)
    # lightScale.append(0)
    # 插值
    interp_func = interp1d(lightScale, currentScale)
    return interp_func,currentScale,lightScale


def linear(interp_func,weight):
    weight = min(1,weight)
    weight = max(0,weight)
    return  interp_func(weight)

