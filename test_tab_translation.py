#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试标签页翻译功能（不启动UI）
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from language_manager import language_manager

def test_tab_translations():
    print("=== 测试标签页翻译功能 ===")
    
    # 测试中文
    print("\n--- 中文标签页 ---")
    language_manager.set_language('zh')
    print(f"数据: '{language_manager.get_text('tab_data')}'")
    print(f"光谱匹配: '{language_manager.get_text('tab_spectral_matching_inner')}'")
    print(f"保存指令: '{language_manager.get_text('tab_save_command_inner')}'")
    print(f"查看数据: '{language_manager.get_text('button_view_data')}'")
    print(f"保存指令按钮: '{language_manager.get_text('button_save_command')}'")
    print(f"匹配数据: '{language_manager.get_text('label_match_data_colon')}'")
    
    # 测试英文
    print("\n--- 英文标签页 ---")
    language_manager.set_language('en')
    print(f"数据: '{language_manager.get_text('tab_data')}'")
    print(f"光谱匹配: '{language_manager.get_text('tab_spectral_matching_inner')}'")
    print(f"保存指令: '{language_manager.get_text('tab_save_command_inner')}'")
    print(f"查看数据: '{language_manager.get_text('button_view_data')}'")
    print(f"保存指令按钮: '{language_manager.get_text('button_save_command')}'")
    print(f"匹配数据: '{language_manager.get_text('label_match_data_colon')}'")

if __name__ == '__main__':
    test_tab_translations()
