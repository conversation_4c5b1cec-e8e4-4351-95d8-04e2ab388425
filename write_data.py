import json


def write_json(light_type,light_status):
    a_file = open("./data.json", "r")
    json_object = json.load(a_file)
    a_file.close()

    json_object[str(light_type)] = light_status

    a_file = open("./data.json", "w")
    json.dump(json_object, a_file)
    a_file.close()


def write_light_json(light_type,light_status):
    a_file = open("./data/light.json", "r")
    json_object = json.load(a_file)
    a_file.close()

    json_object[str(light_type)] = light_status

    a_file = open("./data/light.json", "w")
    json.dump(json_object, a_file)
    a_file.close()


def read_json(data):
    with open("./data.json") as f:
        data_list = json.load(f)
        # print("data_list:",data_list)
    #print(data_list[data])
    return data_list[data]


def read_light_json(data):
    with open("./data/light.json") as f:
        data_list = json.load(f)

    return data_list[data]



base = [str(x) for x in range(10)] + [chr(x) for x in range(ord('A'), ord('A') + 6)]

def dec2hex(num):
    l = []
    if num < 0:
        return '-' + dec2hex(abs(num))
    while True:
        num, rem = divmod(num, 16)
        l.append(base[rem])
        if num == 0:
            return ''.join(l[::-1])


def hex_data(data):
    speed_data = ""
    if len(dec2hex(int(data))) == 1:
        speed_data = "0000000" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 2:
        speed_data = "000000" + dec2hex(int(data))
    elif len(dec2hex(int(int(data)))) == 3:
        speed_data = "00000" + dec2hex(int(int(data)))
    elif len(dec2hex(int(int(data)))) == 4:
        speed_data = "0000" + dec2hex(int(int(data)))
    elif len(dec2hex(int(data))) == 5:
        speed_data = "000" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 6:
        speed_data = "00" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 7:
        speed_data = "0" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 8:
        speed_data = dec2hex(int(data))
    result_data = speed_data#[4:8] + speed_data[0:4]
    return result_data


def hex_data_4(data):
    speed_data = ""
    if len(dec2hex(int(data))) == 1:
        speed_data = "000" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 2:
        speed_data = "00" + dec2hex(int(data))
    elif len(dec2hex(int(int(data)))) == 3:
        speed_data = "0" + dec2hex(int(int(data)))
    elif len(dec2hex(int(int(data)))) == 4:
        speed_data = dec2hex(int(int(data)))

    result_data = speed_data[0:2] + ' '+speed_data[2:4]
    return result_data


import struct
base = [str(x) for x in range(10)] + [chr(x) for x in range(ord('A'), ord('A') + 6)]


def dec2hex(num):
    l = []
    if num < 0:
        return '-' + dec2hex(abs(num))
    while True:
        num, rem = divmod(num, 16)
        l.append(base[rem])
        if num == 0:
            return ''.join(l[::-1])


def hex_to_float(h):
    i = int(h,16)
    return struct.unpack('<f',struct.pack('<I', i))[0]



if __name__ == '__main__':
    dec_num = int('0064', 16)
    print(dec_num)
    pass
    # import cv2
    #
    # img = r'./Result/2022-12-20 163352/1.jpg'
    # img = cv2.imread(img)
    # cv2.imshow("original", img)
    #
    # # 选择ROI
    # roi = cv2.selectROI(windowName="original", img=img, showCrosshair=True, fromCenter=False)
    # x, y, w, h = roi
    # print(roi)
    #
    # # 显示ROI并保存图片
    # if roi != (0, 0, 0, 0):
    #     crop = img[y:y + h, x:x + w]
    #     cv2.imshow("crop", crop)
    #     cv2.imwrite("./images/1.jpg", crop)
    #     print("Saved!")
    #
    # # 退出
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()

    '''
    #write_json('position',[1,2])
    data_x = read_json('position')
    # data_y = read_json('position_y')
    print(data_x)
 #   read_json('position')
    print('ok')
    '''