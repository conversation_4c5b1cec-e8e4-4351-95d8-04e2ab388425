# -*- coding: utf-8 -*-
"""
测试参数自动保存功能
"""

import json
import os

def test_setting_file():
    """测试设置文件的读写功能"""
    print("=== 测试设置文件功能 ===")
    
    # 检查设置文件是否存在
    if os.path.exists("setting.json"):
        print("✓ setting.json 文件存在")
        
        # 读取设置文件
        try:
            with open("setting.json", 'r', encoding='utf-8') as f:
                settings = json.load(f)
            print("✓ 设置文件读取成功")
            
            # 显示当前设置
            print("\n当前设置:")
            for key, value in settings.items():
                if key != "std_data" and key != "protocol":  # 跳过大数据字段
                    print(f"  {key}: {value}")
                    
        except Exception as e:
            print(f"✗ 设置文件读取失败: {e}")
    else:
        print("✗ setting.json 文件不存在")
    
    # 测试写入功能
    try:
        test_settings = {
            "test_parameter": "test_value",
            "channels_num": 31,
            "light_com1": "COM9"
        }
        
        with open("test_setting.json", 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, ensure_ascii=False, indent=2)
        print("✓ 测试写入功能成功")
        
        # 清理测试文件
        os.remove("test_setting.json")
        print("✓ 测试文件清理完成")
        
    except Exception as e:
        print(f"✗ 测试写入功能失败: {e}")

def check_required_parameters():
    """检查必需的参数是否存在"""
    print("\n=== 检查必需参数 ===")
    
    required_params = [
        "channels_num",
        "light_com1", "light_com2", "light_com3", 
        "light_com4", "light_com5", "light_com6",
        "protocol"
    ]
    
    if os.path.exists("setting.json"):
        try:
            with open("setting.json", 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            missing_params = []
            for param in required_params:
                if param not in settings:
                    missing_params.append(param)
                else:
                    print(f"✓ {param}: {settings[param]}")
            
            if missing_params:
                print(f"\n缺少的参数: {missing_params}")
            else:
                print("\n✓ 所有必需参数都存在")
                
        except Exception as e:
            print(f"✗ 检查参数失败: {e}")
    else:
        print("✗ 设置文件不存在，无法检查参数")

def suggest_improvements():
    """建议改进"""
    print("\n=== 功能建议 ===")
    print("1. 参数自动保存功能已实现")
    print("2. 支持的参数类型:")
    print("   - 串口设置 (light_com1~6, spec_com, measure_com, le007_com)")
    print("   - 协议设置 (current_protocol_type)")
    print("   - 设备类型 (measure_device_type)")
    print("   - 通道启用状态 (ch1_enabled~6)")
    print("   - LED数据选择 (selected_led_data)")
    print("   - 匹配设置 (match_method, match_spd_path)")
    print("   - 路径设置 (correction_save_path)")
    print("   - 数值设置 (spd_accuracy, target_luminance, tl84_value, cwf_value, flick_value)")
    print("   - LE007设置 (le007_left_illum)")
    print("3. 启动时会自动加载所有保存的参数")
    print("4. 界面参数更改时会实时保存")

if __name__ == "__main__":
    test_setting_file()
    check_required_parameters()
    suggest_improvements()
    
    print("\n=== 测试完成 ===")
    print("现在可以运行主程序测试参数自动保存功能:")
    print("python main.py")
