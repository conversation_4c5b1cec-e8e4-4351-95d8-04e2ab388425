"""
产生指令
01 00 00 00 25 01 BB 01 C7 01 C7 01 9C 00 03 01 9C 00 02 01 9C 02 87 00 10 00 00 00 00 00 17 00 03 00 4D 00 02 01 9C 00 5F 01 9C 29 0d  //2856

01 00 00 00 97 00 D4 00 CE 00 CE 01 7A 00 03 01 7A 01 08 01 7A 00 83 00 48 00 00 00 00 00 FF 00 03 00 04 01 08 01 7A 02 7D 01 7A 29 0d  //5000

01 00 00 00 B8 00 B1 00 CA 00 CA 01 48 00 03 01 48 01 81 01 48 00 7A 00 54 00 00 00 00 01 1C 00 03 00 0D 01 81 01 48 02 62 01 48 29 0d  //5500

01 00 00 00 F5 00 A5 00 9F 00 9F 00 FC 00 03 00 FC 02 32 00 FC 00 69 00 64 00 00 00 00 01 41 00 03 00 0C 02 32 00 FC 02 14 00 FC 29 0d  //6500

01 00 00 01 33 00 90 00 8E 00 8E 00 C2 00 06 00 C2 02 9C 00 C2 00 5E 00 67 00 00 00 00 01 52 00 05 00 17 02 9C 00 C2 01 FC 00 C2 29 0d  //7500

01 00 00 为起始位
29 0d  为终止位
a[0~100]->0~1000级->16进制
"""
import numpy as np

# 定义初始指令
ORDER = "01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 29 0d 0d 0a"
# 定义通道位置


def getOrder(order, loc, weight):
    """
    获取指令
    oder:
        当前已经存在的指令
    loc:
        需要改变的点位位置
    weight:
        匹配返回的权重,0~1
    """
    # temp = "01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 29 0d 0d 0a"
    pwm = int(np.floor(weight * 1000))
    print('pwm:',pwm)
    pwm = hex(pwm)[2:].zfill(4).upper()
    pwm = pwm[:2] + " " + pwm[2:] + " "
    # 起始占位
    pre = 3*3
    # 字符长度
    lenth = 3*2
    cutPre = pre + lenth * (loc - 1)
    cutEnd = pre + lenth * loc
    strPre = order[:cutPre]
    strEnd = order[cutEnd:]
    order = strPre + pwm + strEnd
    return order


def getOrder16(order,scale):
    # 总调控
    # order 当前指令
    # scale 本次比上次的比例
    # temp = "01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 29 0d 0d 0a"
    # pwm = int(np.floor(weight * 1000))
    # pwm = hex(pwm)[2:].zfill(4).upper()
    # pwm = pwm[:2] + " " + pwm[2:] + " "
    # 起始占位
    pre = 3*3
    # 字符长度
    lenth = 3*2

    for i in range(15):
        indexStart = pre + lenth * i
        indexEnd = pre + lenth * (i+1)
        pwm = order[indexStart:indexEnd]
        # 转化为10进制
        pwm = pwm.replace(" ","")
        pwm = int(pwm,16)
        pwm = int(pwm*scale)
        # 转化16进制
        pwm = hex(pwm)[2:].zfill(4).upper()
        pwm = pwm[:2] + " " + pwm[2:] + " "
        strPre = order[:indexStart]
        strEnd = order[indexEnd:]
        order = strPre + pwm + strEnd

    return order


def analyzeOder(order):
    # 返回最大的value值
    # 起始占位
    pre = 3 * 3
    # 字符长度
    lenth = 3 * 2
    values = []
    for i in range(15):
        indexStart = pre + lenth * i
        indexEnd = pre + lenth * (i + 1)
        pwm = order[indexStart:indexEnd]
        # 转化为10进制
        pwm = pwm.replace(" ", "")
        pwm = int(pwm, 16)
        values.append(pwm)

    return np.max(values)


if __name__ == '__main__':
    import  time
    order = ORDER
    # for i in range(1,15):
    order = getOrder(order, 1, 0.001)
    print(order)
    # ORDER = "01 00 00 0A 0A 0B 0B 0C 0C 0D 0D 0E 0E 0F 0F 0A 0A 0B 0B 0C 0C 0D 0D 0E 0E 0F 0F 0A 0A 0B 0B 29 0d 0d 0a"
    # ORDER = "01 00 00 0A 0A 0b 0b 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 29 0d 0d 0a"
    # order = getOrder15(ORDER,0.5)
    # print(order)


