#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试标签翻译功能
"""

from language_manager import language_manager

def test_label_translations():
    print("=== 测试标签翻译功能 ===")
    
    # 测试中文标签
    print("\n--- 中文标签 ---")
    language_manager.set_language('zh')
    print(f"光源: {language_manager.get_text('label_light_source')}")
    print(f"仪器: {language_manager.get_text('label_instrument')}")
    print(f"端口: {language_manager.get_text('label_port_colon')}")
    print(f"通道: {language_manager.get_text('label_channels_colon')}")
    print(f"节点: {language_manager.get_text('label_nodes_colon')}")
    print(f"保存: {language_manager.get_text('label_save_colon')}")
    print(f"设置光源: {language_manager.get_text('button_set_light_source')}")
    print(f"写入光源: {language_manager.get_text('button_write_to_light_source')}")
    
    # 测试英文标签
    print("\n--- 英文标签 ---")
    language_manager.set_language('en')
    print(f"光源: {language_manager.get_text('label_light_source')}")
    print(f"仪器: {language_manager.get_text('label_instrument')}")
    print(f"端口: {language_manager.get_text('label_port_colon')}")
    print(f"通道: {language_manager.get_text('label_channels_colon')}")
    print(f"节点: {language_manager.get_text('label_nodes_colon')}")
    print(f"保存: {language_manager.get_text('label_save_colon')}")
    print(f"设置光源: {language_manager.get_text('button_set_light_source')}")
    print(f"写入光源: {language_manager.get_text('button_write_to_light_source')}")
    
    # 测试标签页标题
    print("\n--- 标签页标题 ---")
    print(f"多光谱光源: {language_manager.get_text('tab_multispectral_source')}")
    print(f"校准: {language_manager.get_text('tab_calibration')}")
    print(f"光谱匹配: {language_manager.get_text('tab_spectral_matching')}")
    print(f"保存指令: {language_manager.get_text('tab_save_command')}")

    # 测试内部标签页标题
    print("\n--- 内部标签页标题 ---")
    print(f"数据: {language_manager.get_text('tab_data')}")
    print(f"光谱匹配: {language_manager.get_text('tab_spectral_matching_inner')}")
    print(f"保存指令: {language_manager.get_text('tab_save_command_inner')}")

    # 测试新增按钮
    print("\n--- 新增按钮 ---")
    print(f"查看数据: {language_manager.get_text('button_view_data')}")
    print(f"保存指令: {language_manager.get_text('button_save_command')}")
    print(f"匹配数据: {language_manager.get_text('label_match_data_colon')}")

if __name__ == '__main__':
    test_label_translations()
