from PySide2.QtWidgets import *
from PySide2.QtGui import *
from PySide2.QtCore import *
from PySide2.QtUiTools import *
from PySide2.QtCore import QObject, Signal
from order import getOrder
import serial
from serial.tools import list_ports
import matplotlib.pyplot as plt
from threading import Thread
import numpy as np
import time
import json
import os
from colorTrans import RGBToHTMLColor ,wavelength_to_rgb
from linear import getFunc
import pyqtgraph as pg
import csv
from scipy.interpolate import interp1d
from match import m_matchSpd
import inspect
import ctypes
from license import read_dog_stats
import psutil, os

def kill_proc_tree(pid, including_parent=True):    
    parent = psutil.Process(pid)
    for child in parent.children(recursive=True):
        child.kill()
    if including_parent:
        parent.kill()


class Signal_View(QObject):
    error = Signal(str,str)
    note = Signal(str,str)
    warning = Signal(str,str)


class Stats:
    def __init__(self):
        self.ui = QUiLoader().load('./ui/mainwindow.ui')
        self.init_window_signal()
        # if read_dog_stats() == "multiLight":
        #     print('Success!')
        # else:
        #     self.signal.error.emit("权限","请检查加密狗!")
        #     return
        # 界面列表
        self.spd_widget_list = []
        self.spd_checkBox_list = []
        self.spd_slider_list = []
        self.spd_lineEdit_list = []
        # 串口列表
        self.serials_names = []
        # led，array原生数据列表
        self.led_data_arrays = []
        # led 最大通道的array,shape->(-1,self.spd_num)
        self.led_data_max_channel = []
        # 线性函数
        self.linear_func = []
        # 线性电流
        self.linear_current = []
        # 线性亮度
        self.linear_light = []
        # 校正后电流
        self.linear_current_d = []
        # 通信串口
        self.serial = None
        # 当前串口
        self.current_serial = "COM3"
        # 通信协议字典
        self.protocol = None
        # 当前协议
        self.current_protocol = None
        # 标准光谱
        self.std_ledData_dict = None
        # 目标光谱
        self.target_spd = None

        self.slider_draw = True
        # 当前光谱曲线
        self.current_wave = []
        self.match_wave = []
        self.measure_wave = []

        self.read_setting()
        self.init_spd_num()
        self.init_protocol()

        self.init_btn_connect()
        

        
        self.init_spd_widget_list()
        self.init_spd_checkBox_list()
        self.init_spd_slider_list()
        self.init_spd_lineEdit_list()

        self.init_spd_slider_change()
        self.init_spd_lineEdit_press()
        self.init_spd_checkBox_enable()

        self.init_comboBox_ledData()
        self.watch_serial_port()

        self.init_QtGraph()
        
        self.init_ratioButtonGroup()

    def read_setting(self):
        with open("setting.json", 'r', encoding='utf-8') as f:
            self.setting = json.load(f)

        self.std_ledData_dict = self.setting["std_data"]
    def init_spd_num(self):
        # 从硬件读取灯珠信息，改变界面，目前软件可支持到36通道
        self.spd_num = self.setting["channels_num"]
        str_correct = ""
        for i in range(self.spd_num):
            str_correct+=str(i+1)+","
        str_correct+=str(self.spd_num+1)
        self.ui.lineEdit_channels.setText(str_correct)




    def init_spd_widget_list(self):
        str_ = "self.ui.widget_"
        for i in range(self.spd_num):
            i += 1
            self.spd_widget_list.append(eval(str_+str(i)))

        # 存在一个总调控的widget
        self.spd_widget_list.append(eval(str_+str(self.spd_num)))

        for i in range(self.spd_num+1,37):
            i += 1
            eval(str_+str(i)).setVisible(False)

    def init_spd_checkBox_list(self):
        str_ = "self.ui.checkBox_"
        for i in range(self.spd_num+1):
            i += 1
            self.spd_checkBox_list.append(eval(str_+str(i)))

    def init_spd_slider_list(self):
        str_ = "self.ui.verticalSlider_"
        for i in range(self.spd_num+1):
            i += 1
            self.spd_slider_list.append(eval(str_+str(i)))

    def init_spd_lineEdit_list(self):
        str_ = "self.ui.lineEdit_"
        for i in range(self.spd_num+1):
            i += 1
            self.spd_lineEdit_list.append(eval(str_+str(i)))

    def init_spd_slider_change(self):
        #todo:发送数据接口,只要slider发生改变就发送数据
        global spd_slider_index
        for spd_slider_index in range(self.spd_num+1):
            def sliderChange(n,value):
                if n == self.spd_num:
                    print("总调控单独控制")
                else:
                    # 获取所有通道的值
                    all_value = [x.value()/1000 for x in self.spd_slider_list][:-1]
                    self.current_wave = np.sum(self.led_data_max_channel * all_value,1)
                    if self.slider_draw == True:
                        self.graphUpdate(np.arange(421)+360,self.current_wave,self.match_wave,self.measure_wave)
                self.spd_lineEdit_list[n].setText(str(value/10))
            self.spd_slider_list[spd_slider_index].valueChanged.connect(lambda x,n=spd_slider_index: sliderChange(n,x))

            def sliderRelease(n):
                if self.current_protocol != None:
                    if n == self.spd_num:
                        print("总调控单独控制")
                    else:
                        # 刷新指令
                        self.current_protocol = getOrder(self.current_protocol,n+1,self.spd_slider_list[n].value()/1000)
                        self.ui.textBrowser_message.append("\n[INFO] %s \n%d 通道发送数据: %d [0-1000]"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),n+1,self.spd_slider_list[n].value()))
                        self.ui.textBrowser_message.append("\n[SEND] %s \n%s"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),self.current_protocol))
                        self.serial.write(bytes.fromhex(self.current_protocol))
                        self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
            
            self.spd_slider_list[spd_slider_index].sliderReleased.connect(lambda n=spd_slider_index: sliderRelease(n))

    def init_spd_lineEdit_press(self):
        # 触发slider改变，与发送数据
        global spd_lineEdit_index
        for spd_lineEdit_index in range(self.spd_num+1):
            def lineEditPress(n):
                value = eval(self.spd_lineEdit_list[n].text())
                value = round(value,1)
                self.spd_lineEdit_list[n].setText(str(value))
                value = min(value*10,1000)
                
                if n == self.spd_num:
                    print("总调控单独控制-press")
                else:
                    # 刷新指令
                    try:
                        self.current_protocol = getOrder(self.current_protocol,n+1,value/1000)
                        self.ui.textBrowser_message.append("\n[INFO] %s \n%d 通道发送数据: %d [0-1000]"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),n+1,value))
                        self.ui.textBrowser_message.append("\n[SEND] %s \n%s"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),self.current_protocol))
                        self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                        self.serial.write(bytes.fromhex(self.current_protocol))
                    except:
                        self.ui.textBrowser_message.append("\n[WARING] %s \n没有选择设备，暂无法发送数据!"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))
                        self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    finally:
                        self.spd_slider_list[n].setValue(value)

            self.spd_lineEdit_list[spd_lineEdit_index].returnPressed.connect(lambda n=spd_lineEdit_index: lineEditPress(n))
    
    def init_spd_checkBox_enable(self):
        global spd_checkBox_index
        for spd_checkBox_index in range(self.spd_num+1):
            def checked(n,x):
                if x==0:
                    # 失活
                    self.spd_slider_list[n].setEnabled(False)
                    self.spd_lineEdit_list[n].setEnabled(False)
                    # 置零
                    self.spd_slider_list[n].setValue(0)
                    self.spd_lineEdit_list[n].setText("0.0")
                    # 发送数据
                    if n == self.spd_num:
                        print("发送数据全0")
                    else:
                        print("%d 通道发送数据: %.1f"%(n+1,self.spd_slider_list[n].value()/10))
                if x== 2:
                    self.spd_slider_list[n].setEnabled(True)
                    self.spd_lineEdit_list[n].setEnabled(True)
            self.spd_checkBox_list[spd_checkBox_index].stateChanged.connect(lambda x,n=spd_checkBox_index: checked(n,x))

    def watch_serial_port(self):
        # 观测串口
        # def watch():
        # while(True):
        # 初始化串Q口连接显示
        port_list = list(list_ports.comports())
        port_list_name = []
        for each_port in port_list:
            port_list_name.append(each_port[0])

        # 清零重新添加
        self.ui.comboBox_serials.clear()
        for name in port_list_name:
            self.ui.comboBox_serials.addItem(name)
            # 如果减少
            # if len(port_list_name) < len(self.serials_names):
            #     serials_reduce = set(self.serials_names) - set(port_list_name) 
            #     if self.current_serial in list(serials_reduce):
            #         self.signal.error.emit("错误!","当前连接端口：%s \n已拔出!"%self.current_serial)
            #         self.ui.textBrowser_message.append("[ERROR]串口: %s 失去连接"%self.current_serial)
            #         self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
            #         self.ui.pushButton_serials_connect.setText("连接")
            #         self.ui.comboBox_serials.setCurrentIndex(-1)
            #         self.serial.close()

            # self.serials_names = port_list_name
            # self.ui.comboBox_serials.update()
            # time.sleep(0.3)
        # watch_thread = Thread(target=watch)
        # watch_thread.start()
        
    def init_btn_connect(self):
        def connect():
            if self.ui.pushButton_serials_connect.text() == "连接":
                try:
                    self.serial = serial.Serial(self.ui.comboBox_serials.currentText(), 115200, bytesize=8, parity='N', stopbits=1, timeout=0.1)
                    self.current_protocol = self.protocol[self.ui.comboBox_protocol.currentText()]
                    self.ui.pushButton_serials_connect.setText("断开")
                    self.ui.textBrowser_message.append("\n[INFO] %s \n连接串口: %s"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),self.ui.comboBox_serials.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                except:
                    self.signal.error.emit("错误!","%s 连接失败!"%self.ui.comboBox_serials.currentText())
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n连接串口: %s 失败"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),self.ui.comboBox_serials.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

            elif self.ui.pushButton_serials_connect.text() == "断开":
                try:
                    self.serial.close()
                    self.current_protocol = None
                    self.ui.pushButton_serials_connect.setText("断开")
                    self.ui.textBrowser_message.append("\n[INFO] %s \n断开串口: %s"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),self.ui.comboBox_serials.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                except:
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n断开串口: %s 失败"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),self.ui.comboBox_serials.currentText()))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

                self.ui.pushButton_serials_connect.setText("连接")
        self.ui.pushButton_serials_connect.clicked.connect(connect)

        def show_led_data():
            # 显示峰值亮度与线性化程度
            led_datas = []
            led_linear_x = []
            led_linear_y = []
            for led_data in self.led_data_arrays:
                led_datas.append(led_data[1:,0])
                led_linear_x.append(led_data[0,:])
                led_linear_y.append(np.max(led_data[1:,],0))

            led_datas = np.reshape(led_datas,(self.spd_num,-1))
            x = np.linspace(360,781,421)
            fig1 = plt.figure("Multi-channel energy value",figsize=(7,7*0.618))
            ax1 = fig1.add_subplot(111)
            ax1.patch.set_facecolor('#666666')
            fig1.patch.set_facecolor('#666666')

            fig2 = plt.figure("Multi-channel linear",figsize=(10,10*0.618))
            ax2 = fig2.add_subplot(111)
            ax2.patch.set_facecolor('#666666')
            fig2.patch.set_facecolor('#666666')

            for data,linear_x,linear_y in zip(led_datas,led_linear_x,led_linear_y):
                peek = np.argmax(data)+360
                rgb = wavelength_to_rgb(peek)
                html = RGBToHTMLColor(rgb)
                ax1.plot(x,data,color=html)
                ax1.text(peek,np.max(data),str(peek),color=html,fontsize=7,weight="black")
                ax2.plot(linear_x,linear_y,color=html,label=str(peek),marker="*")
                ax2.text(linear_x[0]+10,linear_y[0],str(peek),color=html,fontsize=7,weight="black")
            ax2.set_xticks(led_linear_x[0])    
            plt.xticks(fontsize=7,color='white',rotation=65)
            # plt.tick_params(axis='x',colors='white')
            plt.legend(loc=2,fontsize=7)
            # plt.subplots_adjust(left=0.05, right=0.95, top=0.95, bottom=0.05)
            plt.show()
        self.ui.pushButton_watch_led.clicked.connect(show_led_data)

        def spdImport():
            filePath, _  = QFileDialog.getOpenFileName(
                                                    self.ui,             # 父窗口对象
                                                    "选择光谱数据", # 标题
                                                    r"./",        # 起始目录
                                                    "*.csv")
            self.ui.lineEdit_MatchSpdPath.setText(filePath)
        self.ui.pushButton_MatchSpdImport.clicked.connect(spdImport)

         # 测量目标数据
        def measureTargetSpd():
            def measureRun0():
                # 至灰
                self.ui.pushButton_MatchSpdMeasure.setEnabled(False)
                try:
                    # 检测是否含有已存在文件
                    if os.path.exists("./Measurement.csv"):
                        os.remove("./Measurement.csv")
                    # 调用测量
                    os.system("Measurement.exe")
                    time.sleep(1)
                    with open('Measurement.csv', 'r') as f:
                        reader = csv.reader(f)
                        for index, row in enumerate(reader):
                            # 记录展示光谱值
                            if index == 0:
                                Spc = [eval(x) for x in row[1:] if x != ""]
                                # 保存
                                if not os.path.exists("./spdTemp"):
                                    os.makedirs("./spdTemp")
                                timeRecord = time.strftime("%Y-%m-%d-%H:%M:%S", time.localtime())
                                savePath = "./spdTemp/" + timeRecord + ".csv"
                                savePath = savePath.replace("-","_")
                                savePath = savePath.replace(":","_")
                                self.measure_wave = Spc
                                self.graphUpdate(np.linspace(360,780,421),self.current_wave,self.match_wave,self.measure_wave)
                                np.savetxt(savePath, Spc)
                                self.ui.lineEdit_MatchSpdPath.setText(savePath)
                    # 弹出测量成功
                    self.signal.note.emit('提示','CL500A测量成功!')
                    self.ui.textBrowser_message.append("\n[INFO] %s \nCL500A测量完毕!\n 保存路径：%s"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),savePath))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                except:
                    # 检查500是否正常
                    self.signal.error.emit("错误", "请检查500A是否正常!")
                    self.ui.textBrowser_message.append("\n[ERROR] %s \nCL500A测量失败!请检查500A是否正常!"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                # 正常
                self.ui.pushButton_MatchSpdMeasure.setEnabled(True)

            mearsureThread0 = Thread(target=measureRun0)
            mearsureThread0.start()
        self.ui.pushButton_MatchSpdMeasure.clicked.connect(measureTargetSpd)

        # 测量多通道数据
        def measureChannels():
            def measureChannelsRun():
                # 获取测试节点
                channels = [eval(x) for x in self.ui.lineEdit_channels.text().split(",")]
                notes = [eval(x) for x in self.ui.lineEdit_notes.text().split(",")]
                self.ui.progressBar.setRange(0,len(channels)*len(notes))
                # 检查保存路径
                savePath = self.ui.lineEdit_correctionSavePath.text()
                timeRecord = time.strftime("%Y-%m-%d-%H:%M:%S", time.localtime())
                timeRecord = timeRecord.replace("-", "_")
                timeRecord = timeRecord.replace(":", "_")
                savePath = os.path.join(savePath, timeRecord)
                os.mkdir(savePath)

                self.ui.pushButton_startCorrection.setEnabled(False)
                self.ui.textBrowser_message.append("\n[INFO] %s \n---------------------【CL500A测试】------------------------】"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))
                self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                try:
                    # 检测是否含有已存在文件
                    if os.path.exists("./Measurement.csv"):
                        os.remove("./Measurement.csv")
                    self.serial.write(bytes.fromhex(getOrder(self.protocol[self.ui.comboBox_protocol.currentText()],1,1000/1000)))
                    # 调用测量
                    os.system("Measurement.exe")
                    
                    with open('Measurement.csv', 'r') as f:
                        reader = csv.reader(f)
                        for index, row in enumerate(reader):
                            # 记录展示光谱值
                            if index == 0:
                                Spc = [eval(x) for x in row[1:] if x != ""]
                                # 保存
                                if not os.path.exists("./spdTemp"):
                                    os.makedirs("./spdTemp")
                                timeRecord = time.strftime("%Y-%m-%d-%H:%M:%S", time.localtime())
                                timeRecord = timeRecord.replace("-", "_")
                                timeRecord = timeRecord.replace(":", "_")
                                savePath0 = "./spdTemp/" + timeRecord + ".csv"
                                np.savetxt(savePath0, Spc)
                                # self.ui.lineEdit_MatchSpdPath.setText(savePath0)
                except Exception as err:
                    # 检查500是否正常
                    self.signal.error.emit("错误","请检查500A是否正常！")
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n【---------------------【采集失败】------------------------】\n %s" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),err))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    self.ui.pushButton_startCorrection.setEnabled(True)
                    return

                self.ui.textBrowser_message.append("\n[INFO] %s \n---------------------【CL500A测试正常】------------------------】"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))
                self.ui.textBrowser_message.append("\n[INFO] %s \n---------------------【开始采集】------------------------】"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))
                self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)


                try:
                    for channel in channels:
                        oneSpdList = []
                        for index,note in enumerate(notes):
                            if os.path.exists("./Measurement.csv"):
                                os.remove("./Measurement.csv")

                            # 这里要发送数据
                            self.current_protocol = getOrder(self.protocol[self.ui.comboBox_protocol.currentText()],channel,note/1000)
                            self.serial.write(bytes.fromhex(self.current_protocol))
                            self.ui.textBrowser_message.append("\n[SEND] %s \n%s" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),self.current_protocol))
                            self.ui.textBrowser_message.append("\n[INFO] %s \n【测量】Channel:%d,Note:%d" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),channel, note))
                            self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                            # 发送之后测量
                            measure_flag = os.system("Measurement.exe")
                            # 测量完成后读取csv
                            measure_dict = {}
                            with open('Measurement.csv', 'r') as f:
                                reader = csv.reader(f)
                                for row in reader:
                                    measure_dict[row[0]] = [eval(x) for x in row[1:] if x != ""]
                            # 界面显示
                            spd = measure_dict["Spc"]
                            oneSpdList.append(note)
                            oneSpdList.extend(spd)

                            self.ui.progressBar.setValue((channel-1)*len(notes)+index+1)
                        oneSpdList = list(oneSpdList)
                        oneSpd = np.reshape(oneSpdList, (len(notes), -1)).T
                        savePath0 = os.path.join(savePath, str(channel).zfill(2) + ".csv")
                        np.savetxt(savePath0, oneSpd, delimiter=",")
                        self.ui.textBrowser_message.append("\n[INFO] %s \n【测量】Channel:%d 完毕,保存路径:%s" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),channel,savePath0))
                        self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

                    self.ui.textBrowser_message.append("\n[INFO] %s \n---------------------【采集完成】------------------------】\n保存位置:%s" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),savePath))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    self.signal.note.emit("采集数据", "采集完成！")

                except Exception as err:
                    self.ui.pushButton_startCorrection.setEnabled(True)
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n【---------------------【采集失败】------------------------】\n %s" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),err))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

                    self.signal.error.emit("错误","采集失败！\n请检查配置!")
                    return

                self.ui.pushButton_startCorrection.setEnabled(True)

            self.measureThread = Thread(target=measureChannelsRun)
            self.measureThread.start()

        self.ui.pushButton_startCorrection.clicked.connect(measureChannels)

        def stopMeasureChannels():
            try:
                self.stop_thread(self.measureThread)
                self.ui.textBrowser_message.append("\n[INFO] %s \n【---------------------【停止采集】------------------------】\n" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))
                self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                self.ui.pushButton_startCorrection.setEnabled(True)
            except Exception as err:
                self.ui.textBrowser_message.append("\n[INFO] %s \n【---------------------【停止失败】------------------------】\n %s" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),err))
                self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
        
        self.ui.pushButton_stopCorrection.clicked.connect(stopMeasureChannels)

        def measureCL500A():
            # 500A测量
            def measureCL500ARun():
                try:
                    self.ui.textBrowser_message.append("\n[INFO] %s \n【---------------------【CL500A开始测量】------------------------】\n" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))
                    self.ui.pushButton_measure500A.setEnabled(False)
                    measure_flag = os.system("Measurement.exe")
                    # 测量完成后读取csv
                    self.measure_dict = {}
                    with open('Measurement.csv', 'r') as f:
                        reader = csv.reader(f)
                        for index, row in enumerate(reader):
                            self.measure_dict[row[0]] = [eval(x) for x in row[1:] if x != ""]
                            # 记录展示光谱值
                            if index == 0:
                                Spc = [eval(x) for x in row[1:] if x != ""]
                                # 保存
                                if not os.path.exists("./spdTemp"):
                                    os.makedirs("./spdTemp")
                                timeRecord = time.strftime("%Y-%m-%d-%H:%M:%S", time.localtime())
                                timeRecord = timeRecord.replace("-", "_")
                                timeRecord = timeRecord.replace(":", "_")
                                savePath0 = "./spdTemp/" + timeRecord + ".csv"
                                np.savetxt(savePath0, Spc)
                    # 界面显示
                    self.measure_wave = self.measure_dict["Spc"]
                    self.graphUpdate(np.linspace(360,780,421),self.current_wave,self.match_wave,self.measure_wave)

                    self.ui.lineEdit_Measure_Ev.setText(str(self.measure_dict["Ev"][0]))
                    self.ui.lineEdit_Measure_x.setText(str(self.measure_dict["x"][0]))
                    self.ui.lineEdit_Measure_y.setText(str(self.measure_dict["y"][0]))
                    self.ui.lineEdit_Measure_X.setText(str(self.measure_dict["X"][0]))
                    self.ui.lineEdit_Measure_Y.setText(str(self.measure_dict["Y"][0]))
                    self.ui.lineEdit_Measure_Z.setText(str(self.measure_dict["Z"][0]))
                    self.ui.lineEdit_Measure_T.setText(str(self.measure_dict["T"][0]))
                    self.ui.lineEdit_Measure_duv.setText(str(self.measure_dict["duv"][0]))
                    self.ui.lineEdit_Measure_PW.setText(str(self.measure_dict["PW"][0]))
                    self.ui.lineEdit_Measure_DW.setText(str(self.measure_dict["DW"][0]))

                    # 置为Ra
                    self.ui.comboBox_Measure_Re.setCurrentIndex(0)

                    self.ui.lineEdit_Measure_Re.setText(str(self.measure_dict["Re"][0]))
                    self.ui.lineEdit_Measure_Pe.setText(str(self.measure_dict["Pe"][0]))
                    self.ui.lineEdit_Measure_Es.setText(str(self.measure_dict["Es"][0]))
                    self.ui.lineEdit_Measure_SP.setText(str(self.measure_dict["SP"][0]))
                    self.ui.lineEdit_Measure_u.setText(str(self.measure_dict["u"][0]))
                    self.ui.lineEdit_Measure_v.setText(str(self.measure_dict["v"][0]))

                    self.ui.textBrowser_message.append("\n[INFO] %s \n【---------------------【CL500A测量结束】------------------------】\n" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))
                except Exception as err:
                    self.ui.textBrowser_message.append("\n[INFO] %s \n【---------------------【CL500A测量失败】------------------------】\n%s" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),err))
                    self.signal.error.emit("错误", "500A环境异常！")
                finally:
                    self.ui.pushButton_measure500A.setEnabled(True)

            measure500AThread = Thread(target=measureCL500ARun)
            measure500AThread.start()

        self.ui.pushButton_measure500A.clicked.connect(measureCL500A)

        def matchSpd():
            # try:
            # 匹配关键步骤
            # 统计参与匹配的信息
            check_flag = [checkbox.isChecked() for checkbox in self.spd_checkBox_list[:-1]]
            # led 最大通道的array,shape->(-1,self.spd_num)
            tempMax = self.led_data_max_channel
            # shape->(spd_num,-1)
            tempMax = tempMax.T

            match_led_data = []

            for flag,led_spd in zip(check_flag,tempMax):
                if flag == True:
                    match_led_data.append(led_spd)
            
            # shape -> (-1,select_num)
            match_led_data = np.reshape(match_led_data,(np.sum(check_flag),-1)).T

            choose_name = self.ui.buttonGroup_choose_spd.checkedButton().text()
            if choose_name == "Other":
                path = self.ui.lineEdit_MatchSpdPath.text()
                match_std_data = np.loadtxt(path,delimiter=",")
            else:
                match_std_data = self.std_ledData_dict[choose_name]

            match_method = self.ui.comboBox_MatchMethod.currentText()
            match_accuracy = self.ui.spinBox_SpdAccuracy.value()
            match_tagetLuminance = self.ui.spinBox_tagetLuminance.value()   
            a,newSpd,insertLEDData,insertSTDData,T,XYZ,x,y,cd,scale = m_matchSpd(match_led_data,match_std_data,match_accuracy,match_tagetLuminance,match_method)
            self.ui.textBrowser_message.append("\n[INFO] %s \n匹配完毕!\n" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))

            # 整理权重参与
            self.slider_draw = False
            indexs = np.where([not x for x in check_flag])[0]
            a_list = list(a)
            for index in indexs:
                a_list.insert(index,0)
            if self.ui.radioButton_send_true.isChecked():
                # 需要发送数
                correct_a = [func(a0) for a0,func in zip(a_list,self.linear_func)]
                self.current_protocol = self.protocol[self.ui.comboBox_protocol.currentText()]
                for channel,a_ in enumerate(correct_a):

                    self.current_protocol = getOrder(self.current_protocol,channel+1,a_)
                    self.spd_slider_list[channel].setValue(int(a_ * 1000))
                
                self.ui.textBrowser_message.append("\n[SEND] %s \n%s"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),self.current_protocol))
                self.serial.write(bytes.fromhex(self.current_protocol))
            # 更新界面
            self.current_wave = np.sum(self.led_data_max_channel * np.asarray(a_list),1)
            self.match_wave = insertSTDData
            self.graphUpdate(np.arange(421)+360,self.current_wave,self.match_wave,self.measure_wave)
            self.slider_draw = True
            # except Exception as err:
            #     self.ui.textBrowser_message.append("\n[ERROR] %s \n匹配失败!\n %s" % (time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),err))

        self.ui.pushButton_matchStart.clicked.connect(matchSpd)

        def saveOrder():
            if not os.path.exists("./tempData"):
                os.makedirs("./tempData")
            path,_ = QFileDialog.getSaveFileName(self.ui, "选择文件", "./tempData","file(*.txt)")

            with open(path,"w",encoding="utf-8") as f:
                f.writelines(str(self.current_protocol))
                self.ui.textBrowser_message.append("\n[INFO] %s \n保存完毕!\n 保存指令：%s"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),self.current_protocol))
                self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

        self.ui.pushButton_saveOrder.clicked.connect(saveOrder)

        def importOrder():
            path,_ = QFileDialog.getOpenFileName(self.ui, "选择文件", "./tempData","file(*.txt)")

            with open(path,"r",encoding="utf-8") as f:
                current_protocol = f.readlines()[0]
                self.current_protocol = current_protocol
                self.ui.textBrowser_message.append("\n[INFO] %s \n读取完毕!\n 读取指令：%s"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),self.current_protocol))
                self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)

                # 发送指令
                self.serial.write(bytes.fromhex(self.current_protocol))

        self.ui.pushButton_importOrder.clicked.connect(importOrder)


    def init_window_signal(self):
        def emitError(str1,str2):
            QMessageBox.critical(self.ui,str1,str2)
        def emitNote(str1,str2):
            QMessageBox.information(self.ui,str1,str2)
        def emitWarning(str1,str2):
            QMessageBox.warning(self.ui,str1,str2)
        self.signal = Signal_View()
        self.signal.error.connect(emitError)
        self.signal.note.connect(emitNote)
        self.signal.warning.connect(emitWarning)

    def init_protocol(self):
        self.protocol = self.setting["protocol"]
        keys = self.protocol.keys()
        for key in keys:
            self.protocol[key] += " 00"*(self.spd_num)*2
            self.protocol[key] += " 29 0d 0d 0a"
        self.ui.comboBox_protocol.addItems(keys)

    def init_comboBox_ledData(self):
        led_data_names = os.listdir("./ledData")
        self.ui.comboBox_ledData.addItems(led_data_names)
        self.ui.comboBox_ledData.setCurrentIndex(-1)

        def read_led_data():
            self.led_data_arrays = []
            self.linear_func = []
            self.linear_current = []
            self.linear_light = []
            self.led_data_max_channel = []

            led_name = self.ui.comboBox_ledData.currentText()
            led_dir = os.path.join("./ledData",led_name)
            led_channels = os.listdir(led_dir)
            led_channels = [x for x in led_channels if x.endswith(".csv")]
            led_channels = sorted(led_channels)
            led_paths = [os.path.join(led_dir,x) for x in led_channels]

            if len(led_paths) != self.spd_num:
                if  self.ui.comboBox_ledData.currentIndex() != -1:
                    self.ui.textBrowser_message.append("\n[ERROR] %s \n通道数不一致!\n设置:%d\n导入:%d"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),self.spd_num,len(led_paths)))
                    self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                    self.signal.error.emit("错误!","通道数不一致!\n设置:%d\n导入:%d"%(self.spd_num,len(led_paths)))
                self.ui.comboBox_ledData.setCurrentIndex(-1)
                
            else:
                self.ui.textBrowser_message.append("\n[INFO] %s \n导入数据成功! 路径:%s"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),led_dir))
                self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
                for spd,path in enumerate(led_paths):
                    led_data = np.loadtxt(path,delimiter=",")
                    self.led_data_arrays.append(led_data)
                    # 界面更改
                    led_data = led_data[1:,0]
                    self.led_data_max_channel.append(led_data)

                    peek = np.argmax(led_data) + 360
                    self.spd_checkBox_list[spd].setText(str(peek))
                    rgb = wavelength_to_rgb(peek)
                    html = RGBToHTMLColor(rgb)
                    self.spd_checkBox_list[spd].setStyleSheet("color:#FFFFFF;"+"background-color:"+html)

                # 所有的led最大值数据
                self.led_data_max_channel = np.reshape(self.led_data_max_channel,(self.spd_num,-1)).T
                # 导入后线性化数据
                self.linear()
                self.spd_checkBox_list[self.spd_num].setText("all")

        self.ui.comboBox_ledData.currentIndexChanged.connect(read_led_data)
            
    def linear(self):
        # todo:进行线性化输出
        for led_data in self.led_data_arrays:
            linear_func,linear_current,linear_light = getFunc(led_data)
            self.linear_func.append(linear_func)
            self.linear_current.append(linear_current)
            self.linear_light.append(linear_light)
            self.linear_current_d.append(linear_func(linear_light))
            # plt.plot(linear_current,linear_light,marker="s")
            # plt.plot(linear_func(np.arange(100)/100),np.arange(100)/100,marker="*")
            # plt.xlabel("light")
            # plt.ylabel("current")
            # plt.show()
        # self.signal.note.emit("提示","线性化数据已保存")
        self.ui.textBrowser_message.append("\n[INFO] %s \n线性化校准完毕,数据已保存!"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())))
        self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)


    def init_QtGraph(self):
        self.pw = pg.PlotWidget()
        self.pw.setBackground('#666666')
        self.p1 = self.pw.plotItem
        self.p2 = pg.ViewBox()
        self.p1.setLabels(left='Spd Value')
        self.p1.showAxis('right')
        self.p1.scene().addItem(self.p2)
        # p1.getAxis('right').linkToView(p2)
        self.p2.setXLink(self.p1)
        # p1.getAxis('right').setLabel('axis2', color='#0000ff')
        self.p1.vb.sigResized.connect(self.updateViews)
        target_spd = self.std_ledData_dict["A"]

        # 界面显示
        x = np.linspace(360,780,np.size(target_spd))
        f = interp1d(x,target_spd)
        x = np.linspace(360,780,421)
        y = f(x)
        self.match_wave = y
        self.graphUpdate(x,self.current_wave,self.match_wave,self.measure_wave)

        # Grid = pg.GridItem()
        # Grid.setPen(color='g',width=0.4,style=QtCore.Qt.DashLine)       # 网格线的风格（颜色、粗细、风格）
        # Grid.setTickSpacing(x=[5])     # 网格线之间的间隔
        # Grid.setTextPen(color='r')          # 网格刻度值的风格设置
        # self.p1.vb.addItem(Grid)

    def updateViews(self):
        ## view has resized; update auxiliary views to match
        self.p2.setGeometry(self.p1.vb.sceneBoundingRect())
        ## need to re-update linked axes since this was called
        ## incorrectly while views had different shapes.
        ## (probably this should be handled in ViewBox.resizeEvent)
        self.p2.linkedViewChanged(self.p1.vb, self.p2.XAxis)

    def graphUpdate(self,spd_x,light_wave=[],match_wave=[],measure_wave = []):
        """
        主窗口更新
        """
        self.p1.clear()
        self.updateViews()
        #theo = []
        # for t in spd_x:theo.append(0)
        # if np.size(light_wave)!=0:
        #     self.p1.plot(spd_x,light_wave/np.max(light_wave),pen=pg.mkPen(width=2,color="#0000ff"),name="Match")
        if np.size(match_wave)!=0:
            self.p1.plot(spd_x,match_wave/np.max(match_wave),pen=pg.mkPen(width=2,color="#00ff00"),name="Target")
        if np.size(measure_wave)!=0:
            self.p1.plot(spd_x,measure_wave/np.max(measure_wave),pen=pg.mkPen(width=2,color="#ff0000"),name="Measure")


        y = self.p1.getAxis('bottom') 
        x = list(np.linspace(360,780,22)) 
        strs = [str(int(x0)) for x0 in x]
        ticks = [[i, j] for i, j in zip(x,strs)]
        y.setTicks([ticks])            
        self.p1.addLegend()
        self.p1.showGrid(x=True, y=True)
        self.p1._updateView()
        vbox = QHBoxLayout()
        vbox.addWidget(self.pw)
        # 设置全局layout
        self.ui.groupBox_spd_plot.setLayout(vbox)

    def init_ratioButtonGroup(self):
        def choose():
            choose_name = self.ui.buttonGroup_choose_spd.checkedButton().text()
            self.ui.textBrowser_message.append("\n[INFO] %s \n匹配选项按钮组选择:%s"%(time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime()),choose_name))
            self.ui.textBrowser_message.moveCursor(self.ui.textBrowser_message.textCursor().End)
            if choose_name == "Other":
                self.ui.lineEdit_MatchSpdPath.setEnabled(True)
                self.ui.pushButton_MatchSpdImport.setEnabled(True)
                self.ui.pushButton_MatchSpdMeasure.setEnabled(True)
            else:
                self.ui.lineEdit_MatchSpdPath.setEnabled(False)
                self.ui.pushButton_MatchSpdImport.setEnabled(False)
                self.ui.pushButton_MatchSpdMeasure.setEnabled(False)
                target_spd = self.std_ledData_dict[choose_name]

                # 界面显示
                x = np.linspace(360,780,np.size(target_spd))
                f = interp1d(x,target_spd)
                x = np.linspace(360,780,421)
                y = f(x)
                self.match_wave = y
                self.graphUpdate(x,self.current_wave,self.match_wave,self.measure_wave)

        self.ui.buttonGroup_choose_spd.buttonClicked.connect(choose)

    def _async_raise(self,tid, exctype):
        """raises the exception, performs cleanup if needed"""
        tid = ctypes.c_long(tid)
        if not inspect.isclass(exctype):
            exctype = type(exctype)
        res = ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exctype))
        if res == 0:
            raise ValueError("invalid thread id")
        elif res != 1:
            # """if it returns a number greater than one, you're in trouble,
            # and you should call it again with exc=NULL to revert the effect"""
            ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
            raise SystemError("PyThreadState_SetAsyncExc failed")

    def stop_thread(self,thread):
        self._async_raise(thread.ident, SystemExit)

app = QApplication([])
stats = Stats()
stats.ui.show()
app.exec_()


me = os.getpid()
kill_proc_tree(me)