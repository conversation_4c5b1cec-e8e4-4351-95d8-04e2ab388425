#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的 PyInstaller 构建脚本
解决 scipy 和其他依赖的导入问题
"""

import os
import sys
import subprocess
import shutil

def clean_build():
    """清理之前的构建文件"""
    print("清理之前的构建文件...")
    
    dirs_to_remove = ['build', 'dist', '__pycache__']
    files_to_remove = ['*.spec']
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已删除目录: {dir_name}")
    
    # 删除 spec 文件
    for file in os.listdir('.'):
        if file.endswith('.spec'):
            os.remove(file)
            print(f"已删除文件: {file}")

def install_dependencies():
    """安装必要的依赖"""
    print("检查并安装依赖...")
    
    dependencies = [
        'pyinstaller',
        'scipy',
        'numpy', 
        'matplotlib',
        'pyqtgraph',
        'pyserial',
        'PyQt5',
    ]
    
    for dep in dependencies:
        try:
            print(f"安装/更新: {dep}")
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '--upgrade', dep
            ], check=True, capture_output=True, text=True)
            print(f"✓ {dep}")
        except subprocess.CalledProcessError as e:
            print(f"✗ {dep} 安装失败")

def build_with_pyinstaller():
    """使用 PyInstaller 构建可执行文件"""
    print("开始构建可执行文件...")
    
    # PyInstaller 命令参数
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',
        '--windowed',
        '--name', 'ColorSpace_Multispectral',
        '--clean',
        
        # scipy 相关的隐藏导入
        '--hidden-import', 'scipy',
        '--hidden-import', 'scipy.interpolate',
        '--hidden-import', 'scipy.interpolate.interpolate',
        '--hidden-import', 'scipy.interpolate._interpolate',
        '--hidden-import', 'scipy.special',
        '--hidden-import', 'scipy.special._ufuncs_cxx',
        '--hidden-import', 'scipy.linalg',
        '--hidden-import', 'scipy.linalg.cython_blas',
        '--hidden-import', 'scipy.linalg.cython_lapack',
        '--hidden-import', 'scipy.sparse',
        '--hidden-import', 'scipy.sparse.csgraph',
        '--hidden-import', 'scipy.sparse.csgraph._validation',
        '--hidden-import', 'scipy.ndimage',
        
        # numpy 相关的隐藏导入
        '--hidden-import', 'numpy',
        '--hidden-import', 'numpy.core',
        '--hidden-import', 'numpy.core._multiarray_umath',
        '--hidden-import', 'numpy.random',
        '--hidden-import', 'numpy.random._pickle',
        '--hidden-import', 'numpy.random._common',
        '--hidden-import', 'numpy.random._bounded_integers',
        '--hidden-import', 'numpy.random._mt19937',
        '--hidden-import', 'numpy.random._philox',
        '--hidden-import', 'numpy.random._pcg64',
        '--hidden-import', 'numpy.random._sfc64',
        '--hidden-import', 'numpy.random._generator',
        '--hidden-import', 'numpy.random.bit_generator',
        '--hidden-import', 'numpy.random.mtrand',
        
        # matplotlib 相关
        '--hidden-import', 'matplotlib',
        '--hidden-import', 'matplotlib.pyplot',
        '--hidden-import', 'matplotlib.backends',
        '--hidden-import', 'matplotlib.backends.backend_qt5agg',
        
        # pyqtgraph 相关
        '--hidden-import', 'pyqtgraph',
        '--hidden-import', 'pyqtgraph.graphicsItems',
        '--hidden-import', 'pyqtgraph.widgets',
        
        # serial 相关
        '--hidden-import', 'serial',
        '--hidden-import', 'serial.tools',
        '--hidden-import', 'serial.tools.list_ports',
        
        # PyQt5 相关
        '--hidden-import', 'PyQt5',
        '--hidden-import', 'PyQt5.QtCore',
        '--hidden-import', 'PyQt5.QtGui',
        '--hidden-import', 'PyQt5.QtWidgets',
        '--hidden-import', 'PyQt5.uic',
        
        # 收集子模块
        '--collect-submodules', 'scipy',
        '--collect-submodules', 'numpy',
        '--collect-submodules', 'matplotlib',
        '--collect-submodules', 'pyqtgraph',
        '--collect-submodules', 'PyQt5',
        
        # 添加数据文件
        '--add-data', 'translations;translations',
        '--add-data', 'ledData;ledData',
        '--add-data', 'tempData;tempData',
        '--add-data', 'CIE_illminants;CIE_illminants',
        '--add-data', 'data;data',
        '--add-data', 'Source;Source',
        '--add-data', 'ui;ui',
        '--add-data', '*.dll;.',
        '--add-data', '*.lib;.',
        '--add-data', '*.csv;.',
        '--add-data', '*.json;.',
        '--add-data', '*.ico;.',
        
        'main.py'
    ]
    
    # 如果存在图标文件，添加图标
    if os.path.exists('icon.ico'):
        cmd.extend(['--icon', 'icon.ico'])
    
    try:
        print("执行 PyInstaller 命令...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 构建成功！")
            print("可执行文件位置: dist\\ColorSpace_Multispectral.exe")
            return True
        else:
            print("✗ 构建失败！")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"构建过程中出现异常: {e}")
        return False

def main():
    """主函数"""
    print("=== ColorSpace Multispectral 构建工具 ===")
    print()
    
    # 1. 清理构建文件
    clean_build()
    print()
    
    # 2. 安装依赖
    install_dependencies()
    print()
    
    # 3. 构建可执行文件
    success = build_with_pyinstaller()
    print()
    
    if success:
        print("=== 构建完成 ===")
        print("您可以在 dist 目录中找到可执行文件。")
    else:
        print("=== 构建失败 ===")
        print("请检查错误信息并重试。")
    
    input("按回车键退出...")

if __name__ == "__main__":
    main()
