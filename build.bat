@echo off
chcp 65001
echo === ColorSpace Multispectral 构建脚本 ===
echo.

echo 1. 清理之前的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "*.spec" del "*.spec"

echo 2. 安装/更新必要依赖...
pip install --upgrade pyinstaller
pip install --upgrade scipy
pip install --upgrade numpy
pip install --upgrade matplotlib
pip install --upgrade pyqtgraph
pip install --upgrade pyserial
pip install --upgrade PyQt5

echo 3. 使用 PyInstaller 构建...
pyinstaller ^
    --onefile ^
    --windowed ^
    --name "ColorSpace_Multispectral" ^
    --hidden-import scipy ^
    --hidden-import scipy.interpolate ^
    --hidden-import scipy.interpolate.interpolate ^
    --hidden-import scipy.interpolate._interpolate ^
    --hidden-import scipy.special ^
    --hidden-import scipy.special._ufuncs_cxx ^
    --hidden-import scipy.linalg ^
    --hidden-import scipy.linalg.cython_blas ^
    --hidden-import scipy.linalg.cython_lapack ^
    --hidden-import scipy.sparse ^
    --hidden-import scipy.sparse.csgraph ^
    --hidden-import scipy.sparse.csgraph._validation ^
    --hidden-import scipy.ndimage ^
    --hidden-import numpy ^
    --hidden-import numpy.core ^
    --hidden-import numpy.core._multiarray_umath ^
    --hidden-import numpy.random ^
    --hidden-import numpy.random._pickle ^
    --hidden-import numpy.random._common ^
    --hidden-import numpy.random._bounded_integers ^
    --hidden-import numpy.random._mt19937 ^
    --hidden-import numpy.random._philox ^
    --hidden-import numpy.random._pcg64 ^
    --hidden-import numpy.random._sfc64 ^
    --hidden-import numpy.random._generator ^
    --hidden-import numpy.random.bit_generator ^
    --hidden-import numpy.random.mtrand ^
    --hidden-import matplotlib ^
    --hidden-import matplotlib.pyplot ^
    --hidden-import matplotlib.backends ^
    --hidden-import matplotlib.backends.backend_qt5agg ^
    --hidden-import pyqtgraph ^
    --hidden-import pyqtgraph.graphicsItems ^
    --hidden-import pyqtgraph.widgets ^
    --hidden-import serial ^
    --hidden-import serial.tools ^
    --hidden-import serial.tools.list_ports ^
    --hidden-import PyQt5 ^
    --hidden-import PyQt5.QtCore ^
    --hidden-import PyQt5.QtGui ^
    --hidden-import PyQt5.QtWidgets ^
    --hidden-import PyQt5.uic ^
    --add-data "translations;translations" ^
    --add-data "ledData;ledData" ^
    --add-data "tempData;tempData" ^
    --collect-submodules scipy ^
    --collect-submodules numpy ^
    --collect-submodules matplotlib ^
    --collect-submodules pyqtgraph ^
    --collect-submodules PyQt5 ^
    main.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ 构建成功！
    echo 可执行文件位置: dist\ColorSpace_Multispectral.exe
    echo.
    pause
) else (
    echo.
    echo ✗ 构建失败！
    echo 请检查错误信息。
    echo.
    pause
)
