# -*- coding: utf-8 -*-
"""
语言管理器
支持中英文切换功能
"""

import json
import os

class LanguageManager:
    def __init__(self):
        self.current_language = 'zh'  # 默认中文
        self.translations = {}
        self.config_file = 'language_config.json'
        self.load_language_config()
        self.load_translations()
    
    def load_translations(self):
        """加载翻译文件"""
        try:
            # 加载中文翻译
            with open('translations/zh.json', 'r', encoding='utf-8') as f:
                self.translations['zh'] = json.load(f)
            
            # 加载英文翻译
            with open('translations/en.json', 'r', encoding='utf-8') as f:
                self.translations['en'] = json.load(f)
        except FileNotFoundError:
            print("Translation files not found, creating default translations...")
            self.create_default_translations()
    
    def create_default_translations(self):
        """创建默认翻译文件"""
        # 创建translations目录
        if not os.path.exists('translations'):
            os.makedirs('translations')
        
        # 中文翻译
        zh_translations = {
            # 窗口标题
            "window_title": "ColorSapce多光谱控制软件v3.7 20250711",
            
            # 连接相关
            "connect": "连接",
            "disconnect": "断开",
            "connection": "连接",
            
            # 操作按钮
            "start": "开始",
            "stop": "停止",
            "save": "保存",
            "import": "导入",
            "export": "导出",
            "measure": "测量",
            "match": "匹配",
            "calibrate": "校准",
            "clear": "清除",
            "send": "发送",
            
            # 标签和组框
            "calibration": "校准",
            "spectral_matching": "光谱匹配",
            "save_command": "保存指令",
            "measurement_500a": "测量-500A",
            "channels": "通道:",
            "nodes": "节点:",
            "save_path": "保存:",
            "match_data": "匹配数据:",
            "match_method": "匹配方式：",
            
            # 按钮文本
            "start_calibration": "开始校准",
            "stop_calibration": "停止校准",
            "start_matching": "开始匹配",
            "import_command": "导入指令",
            "save_command_btn": "保存指令",
            
            # 消息文本
            "connect_success": "连接成功",
            "connect_failed": "连接失败",
            "calibration_complete": "校准完毕",
            "measurement_success": "测量成功",
            "measurement_failed": "测量失败",
            
            # 菜单和设置
            "language": "语言",
            "chinese": "中文",
            "english": "English",
            "settings": "设置"
        }
        
        # 英文翻译
        en_translations = {
            # 窗口标题
            "window_title": "ColorSpace Multispectral Control Software v3.7 20250711",
            
            # 连接相关
            "connect": "Connect",
            "disconnect": "Disconnect", 
            "connection": "Connection",
            
            # 操作按钮
            "start": "Start",
            "stop": "Stop",
            "save": "Save",
            "import": "Import",
            "export": "Export",
            "measure": "Measure",
            "match": "Match",
            "calibrate": "Calibrate",
            "clear": "Clear",
            "send": "Send",
            
            # 标签和组框
            "calibration": "Calibration",
            "spectral_matching": "Spectral Matching",
            "save_command": "Save Command",
            "measurement_500a": "Measurement-500A",
            "channels": "Channels:",
            "nodes": "Nodes:",
            "save_path": "Save Path:",
            "match_data": "Match Data:",
            "match_method": "Match Method:",
            
            # 按钮文本
            "start_calibration": "Start Calibration",
            "stop_calibration": "Stop Calibration", 
            "start_matching": "Start Matching",
            "import_command": "Import Command",
            "save_command_btn": "Save Command",
            
            # 消息文本
            "connect_success": "Connection Successful",
            "connect_failed": "Connection Failed",
            "calibration_complete": "Calibration Complete",
            "measurement_success": "Measurement Successful",
            "measurement_failed": "Measurement Failed",
            
            # 菜单和设置
            "language": "Language",
            "chinese": "中文",
            "english": "English",
            "settings": "Settings",

            # 设置对话框
            "settings_dialog_title": "Settings",
            "serial_settings": "Serial Settings",
            "channel_settings": "Channel Settings",
            "other_settings": "Other Settings",
            "light_source_serial": "Light Source Serial Settings",
            "measure_device_serial": "Measurement Device Settings",
            "dmx_control_settings": "DMX Control Settings",
            "channel_config": "Channel Configuration",
            "communication_protocol": "Communication Protocol",
            "language_settings": "Language Settings",
            "startup_settings": "Startup Settings",
            "data_settings": "Data Settings",
            "refresh_serial": "Refresh Serial",
            "reset_default": "Reset Default",
            "ok": "OK",
            "cancel": "Cancel",
            "show_dialog_on_startup": "Show this settings dialog on startup",
            "auto_connect_on_startup": "Auto connect devices on startup",
            "enable_dmx_control": "Enable DMX Control",

            # 消息文本
            "info": "INFO",
            "error": "ERROR",
            "warning": "WARNING",
            "send": "SEND",
            "msg_channel_send_data": "Channel {} send data: {} [0-1000]",
            "msg_no_device_selected": "No device selected",
            "msg_connect_serial": "Connect serial: {}",
            "msg_connect_success": "Connection successful",
            "msg_connect_failed": "Connection failed",
            "light_source_1": "Connect light source 1 serial:",

            # 设置对话框
            "settings_dialog_title": "参数设置",
            "serial_settings": "串口设置",
            "channel_settings": "通道设置",
            "other_settings": "其他设置",
            "light_source_serial": "光源串口设置",
            "measure_device_serial": "测量设备设置",
            "dmx_control_settings": "DMX控制设置",
            "channel_config": "通道配置",
            "communication_protocol": "通信协议",
            "language_settings": "语言设置",
            "startup_settings": "启动设置",
            "data_settings": "数据设置",
            "refresh_serial": "刷新串口",
            "reset_default": "重置默认",
            "ok": "确定",
            "cancel": "取消",
            "show_dialog_on_startup": "启动时显示此设置对话框",
            "auto_connect_on_startup": "启动时自动连接设备",
            "enable_dmx_control": "启用DMX控制",

            # 消息文本
            "info": "信息",
            "error": "错误",
            "warning": "警告",
            "send": "发送",
            "msg_channel_send_data": "{} 通道发送数据: {} [0-1000]",
            "msg_no_device_selected": "未选择设备",
            "msg_connect_serial": "连接串口: {}",
            "msg_connect_success": "连接成功",
            "msg_connect_failed": "连接失败",
            "light_source_1": "连接光源1串口:"
        }
        
        # 保存翻译文件
        with open('translations/zh.json', 'w', encoding='utf-8') as f:
            json.dump(zh_translations, f, ensure_ascii=False, indent=2)
        
        with open('translations/en.json', 'w', encoding='utf-8') as f:
            json.dump(en_translations, f, ensure_ascii=False, indent=2)
        
        self.translations['zh'] = zh_translations
        self.translations['en'] = en_translations
    
    def load_language_config(self):
        """加载语言配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.current_language = config.get('language', 'zh')
        except FileNotFoundError:
            # 如果配置文件不存在，使用默认语言
            self.current_language = 'zh'

    def save_language_config(self):
        """保存语言配置"""
        config = {'language': self.current_language}
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

    def set_language(self, language_code):
        """设置当前语言"""
        if language_code in self.translations:
            self.current_language = language_code
            self.save_language_config()  # 保存语言设置
            return True
        return False
    
    def get_text(self, key, default=None):
        """获取翻译文本"""
        if self.current_language in self.translations:
            return self.translations[self.current_language].get(key, default or key)
        return default or key
    
    def get_current_language(self):
        """获取当前语言"""
        return self.current_language

# 全局语言管理器实例
language_manager = LanguageManager()
