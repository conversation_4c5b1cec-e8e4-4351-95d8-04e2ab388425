#-*- coding: utf-8 -*-
# Group  :   ColorSpace
# Author :   <PERSON><PERSON>
# Data   :   2024/10/16
# Tool   :   PyCharm


import time
import serial
import binascii
import struct
import numpy as np
import csv
import os


base = [str(x) for x in range(10)] + [chr(x) for x in range(ord('A'), ord('A') + 6)]


def dec2hex(num):
    l = []
    if num < 0:
        return '-' + dec2hex(abs(num))
    while True:
        num, rem = divmod(num, 16)
        l.append(base[rem])
        if num == 0:
            return ''.join(l[::-1])


def hex_data(data):
    result_data = ""

    if len(dec2hex(int(data))) == 1:
        result_data = "0000000" + dec2hex(int(data))
    if len(dec2hex(int(data))) == 2:
        result_data = "000000" + dec2hex(int(data))
    if len(dec2hex(int(data))) == 3:
        result_data = "00000" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 4:
        result_data = "0000" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 5:
        result_data = "000" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 6:
        result_data = "00" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 7:
        result_data = "0" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 8:
        result_data = dec2hex(int(data))
    res = ""
    for i in range(0, len(result_data) - 1, 2):
        res = result_data[i] + result_data[i + 1] + " " + res
    return res


def connect(com_number="com4", type="normal"):  # 连接光源
    global glo_ser
    global test_type
    test_type = "1"

    if type == "T10-A":
        glo_ser = serial.Serial(com_number, 9600, bytesize=7, parity='E', stopbits=1, timeout=0.01)  # 230400
        online_mode_t10()
    elif type == "CS-LM01IR":
        test_type = "None"
        glo_ser = serial.Serial(com_number, 115200, bytesize=8, parity='N', stopbits=1, timeout=0.1)  # 230400
        online_mode()
    else:
        test_type = "CS-LM01"#老版本115200 新版本9600
        print("enter")
        glo_ser = serial.Serial(com_number, 9600, bytesize=8, parity='N', stopbits=1, timeout=0.1)  # 230400
        online_mode()

def disconnect():  # 连接光源
    global glo_ser

    glo_ser.close()


# 联机模式 1
def online_mode():
    if test_type == "None":
        send_data = "8C 00"
    else:
        send_data = "25 00"

    glo_ser.write(bytes.fromhex(send_data))


# 读取串口的值 1
def read_byte_data():
    data_list = []
    while True:

        rec_data = glo_ser.read(1)
        if rec_data != b'':
            pass
        else:
            break

        rec_byte = binascii.b2a_hex(rec_data)  # b'0x40'

        rec_str = rec_byte.decode('utf-8')  # '40'

        data_list.append(rec_str)
    return data_list


# 读采样状态 1
def read_sampling_status():
    if test_type == "None":
        send_data = "8C 03"
    else:
        send_data = "25 03"

    glo_ser.write(bytes.fromhex(send_data))

    data_list = read_byte_data()

    return data_list


# 设置积分模式
def set_timer_mode(cmd=""):
    # 自动
    if test_type == "None":
        if cmd == "auto":
            send_data = "8C 02 01"
        # 锁定
        if cmd == "locking":
            send_data = "8C 02 00"
    else:
        if cmd == "auto":
            send_data = "25 02 01"
        # 锁定
        if cmd == "locking":
            send_data = "25 02 00"
    glo_ser.write(bytes.fromhex(send_data))


# 设置积分时间 ms 2 ,
def set_timer(time="2"):
    time = str(int(time) * 1000)  # 原来单位us

    res = hex_data(time)

    if test_type == "None":
        send_data = "8c 01 " + res
    else:
        send_data = "25 01 " + res

    glo_ser.write(bytes.fromhex(send_data))


# 读波长参数命令
def read_wavelength_param():
    send_data = "8C 0B"

    glo_ser.write(bytes.fromhex(send_data))


# 读光谱标定数据
def read_calibrate_data():
    send_data = "8C 0C"

    glo_ser.write(bytes.fromhex(send_data))


# 写光谱标定数据
def write_calibrate_data():
    send_data = "8C 0D" + "write"

    glo_ser.write(bytes.fromhex(send_data))


# 开始采样命令
def start_sampling(cmd):
    """
    :param cmd:  00 :即停止测量。  01 :即单次测量。  02:即连续测量
    :return:
    """
    send_data = None
    if test_type == "None":
        if cmd == "stop":
            send_data = "8C 25"
        elif cmd == "once":
            send_data = "8C 0E 01"
        elif cmd == "continues":
            send_data = "8C 0E 02"
    else:
        if cmd == "stop":
            send_data = "25 25"
        elif cmd == "once":
            send_data = "25 0E 01"
        elif cmd == "continues":
            send_data = "25 0E 02"

    glo_ser.write(bytes.fromhex(send_data))


# 数据反转
def analyze_data(data):
    res = data[::-1]

    res_list = []
    for i in res:
        # 16 进制转10进制
        c = int(i, 16)
        res_list.append(c)

    s = struct.unpack(">f", bytes(res_list))[0]
    return s


def get_date():
    curtime = time.strftime('%Y-%m-%d ', time.localtime(time.time()))
    return curtime


def get_time():
    curtime = time.strftime('%H:%M:%S ', time.localtime(time.time()))
    return curtime


def read_light_lux_cct(devices_type="",ir_test_mode = False):
    if test_type == "None":
        send_data = "8C 13"
    else:
        send_data = "25 13"#Type

    lux,cct,x,y,z = 0,0,0,0,0

    glo_ser.write(bytes.fromhex(send_data))

    data_list = read_byte_data()

    if devices_type == "CS-LM-01LR":
        lux = int(analyze_data(data_list[40:44]))  # 显示屏开始第一个 40
        cct = int(analyze_data(data_list[48:52]))

    elif devices_type == "CS-LM01IR":
        # 测试红外
        if ir_test_mode:
            lux = round(analyze_data(data_list[156:160]), 4) * 1000
        else:
            lux = int(analyze_data(data_list[40:44]))
        cct = int(analyze_data(data_list[48:52]))
        x = int(analyze_data(data_list[200:204]))
        y = int(analyze_data(data_list[204:208]))
        z = int(analyze_data(data_list[208:212]))

    elif devices_type == "CS-LS-03":
        lux = int(analyze_data(data_list[72:76]))
        cct = int(analyze_data(data_list[80:84]))

    elif devices_type == "CS-LM05":

        lux = int(analyze_data(data_list[56:60]))
        cct = int(analyze_data(data_list[64:68]))
        x = int(analyze_data(data_list[216:220]))
        y = int(analyze_data(data_list[220:224]))
        z = int(analyze_data(data_list[224:228]))


    if lux > 2000000 or lux <0:
        print("照度 > 200000")
        lux = 0

    if cct > 1000000 or cct <0:
        print("色温 > 100000")
        cct = 0

    return lux,cct,x,y,z


# 读测试结果数据命令
def read_test_result_data(savePath):
    if test_type == "None":
        send_data = "8C 13"
    else:
        send_data = "25 13"

    glo_ser.write(bytes.fromhex(send_data))

    data_list = read_byte_data()

    param_name = {
        '闪烁频率(Hz)': "",
        '波动深度(%)': "",
        '闪烁百分比(%)': "",
        '闪烁指数': "",
        '有效照度(lx)': "",
        '峰值照度(lx)': "",
        '照度积分(lx.s)': "",
        '闪光时间(ms)': "",
        '照度E(lx)': "",
        '烛光E(fc)': "",
        '相关色温(K)': "",
        '黑体偏离Duv': "",
        '坐标x,y': "",
        '坐标u,v': "",
        '坐标u\',v\'': "",
        '坐标y0,dy': "",
        '色容差SDCM': "",
        '显色指数Ra': "",
        'R1-5': "",
        'R6-10': "",
        'R11-15': "",
        '辐照度Ee(W/m)': "",
        '明暗视觉比S/P': "",
        '主波长(nm)': "",
        '色纯度(%)': "",
        '半宽度(nm)': "",
        '峰值波(nm)': "",
        '中心波长(nm)': "",
        '质心波长(nm)': "",
        '红色比(%)': "",
        '绿色比(%)': "",
        '蓝色比(%)': "",
        'CIE1931X,Y,Z': "",
        'TLCI-2012': "",
        '积分时间(us)': "",
        '峰值信号': "",
        '暗信号': "",
        '补偿电平': "",
        '起始波长(nm)': "",
        '终止波长(nm)': "",
        '测试日期': get_date(),
        '测试时间': get_time(),

    }

    #print("11111111111",analyze_data(data_list[0:4]))
    param_name["闪烁频率(Hz)"] = round(analyze_data(data_list[40:44]), 1)
    param_name["波动深度(%)"] = round(analyze_data(data_list[44:48]), 1)
    param_name["闪烁百分比(%)"] = round(analyze_data(data_list[48:52]), 1)
    param_name["闪烁指数"] = round(analyze_data(data_list[52:56]), 2)
    param_name["有效照度(lx)"] = round(analyze_data(data_list[40:44]), 2)
    param_name["峰值照度(lx)"] = round(analyze_data(data_list[60:64]), 1)
    param_name["照度积分(lx.s)"] = round(analyze_data(data_list[64:68]), 1)
    param_name["闪光时间(ms)"] = round(analyze_data(data_list[68:72]), 2)
    param_name["照度E(lx)"] = round(analyze_data(data_list[40:44]), 1)
    param_name["烛光E(fc)"] = round(analyze_data(data_list[44:48]), 2)
    param_name["相关色温(K)"] = int(analyze_data(data_list[48:52]))
    param_name["黑体偏离Duv"] = round(analyze_data(data_list[52:56]), 5)
    param_name["坐标x,y"] = round(analyze_data(data_list[56:60]), 4), round(analyze_data(data_list[60:64]), 4)
    param_name["坐标u,v"] = round(analyze_data(data_list[64:68]), 4), round(analyze_data(data_list[68:72]), 4)
    param_name["坐标u\',v\'"] = round(analyze_data(data_list[72:76]), 4), round(analyze_data(data_list[76:80]), 4)
    param_name["坐标y0,dy"] = round(analyze_data(data_list[112:116]), 4), round(analyze_data(data_list[116:120]), 4)
    param_name["色容差SDCM"] = round(analyze_data(data_list[80:84]), 2)
    param_name["显色指数Ra"] = round(analyze_data(data_list[92:96]), 1)
    param_name["R1-5"] = round(analyze_data(data_list[96:100]), 0), round(analyze_data(data_list[100:104]), 0), round(
        analyze_data(data_list[104:108]), 0), \
                         round(analyze_data(data_list[108:112]), 0), round(analyze_data(data_list[112:116]), 0)

    param_name["R6-10"] = round(analyze_data(data_list[116:120]), 0), round(analyze_data(data_list[120:124]), 0), \
                          round(analyze_data(data_list[124:128]), 0), round(analyze_data(data_list[128:132]), 0), \
                          round(analyze_data(data_list[132:136]), 0)

    param_name["R11-15"] = round(analyze_data(data_list[136:140]), 0), round(analyze_data(data_list[140:144]), 0), \
                           round(analyze_data(data_list[144:148]), 0), round(analyze_data(data_list[148:152]), 0), \
                           round(analyze_data(data_list[152:156]), 0)

    param_name["辐照度Ee(W/m)"] = round(analyze_data(data_list[156:160]), 4) * 1000
    param_name["明暗视觉比S/P"] = round(analyze_data(data_list[160:164]), 3)
    param_name["主波长(nm)"] = round(analyze_data(data_list[164:168]), 2)/1000
    param_name["色纯度(%)"] = round(analyze_data(data_list[200:204]), 1)

    param_name["半宽度(nm)"] = round(analyze_data(data_list[204:208]), 1)
    param_name["峰值波(nm)"] = round(analyze_data(data_list[208:212]), 1)
    param_name["中心波长(nm)"] = round(analyze_data(data_list[212:216]), 1)
    param_name["质心波长(nm)"] = round(analyze_data(data_list[216:220]), 1)
    param_name["红色比(%)"] = round(analyze_data(data_list[220:224]), 1)
    param_name["绿色比(%)"] = round(analyze_data(data_list[224:228]), 1)
    param_name["蓝色比(%)"] = round(analyze_data(data_list[228:232]), 1)

    param_name["CIE1931X,Y,Z"] = round(analyze_data(data_list[200:204]), 3), round(analyze_data(data_list[204:208]), 3), \
                                 round(analyze_data(data_list[208:212]), 3)

    param_name["TLCI-2012"] = round(analyze_data(data_list[244:248]), 0)
    param_name["积分时间(us)"] = round(analyze_data(data_list[248:252]), 2)
    param_name["峰值信号"] = round(analyze_data(data_list[252:256]), 0)#

    # param_name["==0==="] = analyze_data(data_list[304:308])
    # param_name["0"] = analyze_data(data_list[308:312])
    # param_name["1"] = analyze_data(data_list[312:316])
    # param_name["2"] = analyze_data(data_list[316:320])
    # param_name["3"] = analyze_data(data_list[320:324])
    # param_name["4"] = analyze_data(data_list[324:328])
    # param_name["5"] = analyze_data(data_list[328:332])
    # param_name["6"] = analyze_data(data_list[332:336])
    # param_name["7"] = analyze_data(data_list[336:340])
    # param_name["8"] = analyze_data(data_list[340:344])
    # param_name["9"] = analyze_data(data_list[344:348])

    param_name["补偿电平"] = round(analyze_data(data_list[260+19*4:264+19*4]), 4)
    #print(222,260+19*4)

    param_name["起始波长(nm)"] = int(analyze_data(data_list[-8:-4]))
    param_name["终止波长(nm)"] = int(analyze_data(data_list[-4:]))
    start = (param_name["起始波长(nm)"])
    end = (param_name["终止波长(nm)"])
    #print(111111111,param_name["补偿电平"])
    # 光谱数据
    param_spect = []
    #param_spect.append('1')

    n = 0
    indexss=0

    for i in range(start, end + 1):
        indexss+=122
        param_spect.append(analyze_data(data_list[336 + n:340 + n])/10) #校准可以OK的

        #param_spect.append((analyze_data(data_list[336 + n:340 + n]) / 10)*1000)
        #param_spect.append(int(analyze_data(data_list[244 + n:248 + n]) / 10 * 10000) / 10000)
        n += 4
    print(indexss)
    if start <= 380:
        start_num = 380 - start
        end_num = 780 - start
        spd = param_spect[start_num:end_num]
        spd = np.asarray(spd)
        #print("spd",spd)
        np.savetxt(savePath, spd, delimiter=",")

    #import matplotlib.pyplot as plt
    # # 列表数据
    # data = param_spect[start_num:end_num+1]
    # # data = [0.1,0.2,0.3,0.4,0.5]
    # print("max",data[0:10])
    # # 绘制曲线
    # plt.plot(data)
    # plt.xlabel('X轴标签')
    # plt.ylabel('Y轴标签')
    # plt.title('曲线图')
    # plt.show()

    # 波长数据
    param_warelength = {}
    for i in range(start, end + 1):
        param_warelength["波长{}".format(i)] = i

    # print(len(param_name))
    for i in param_name:
        print(i,param_name[i])
    
    # for i in param_spect:
    #     print(i,param_spect[i])

    # for i in param_warelength:
    #     print(i,param_warelength[i])
    #print('--------:',param_spect)

    # 写数据
    # 文件路径
    file_path = 'Measurement.csv'

    # 判断文件是否存在
    if os.path.exists(file_path):
        os.remove(file_path)  # 删除文件
        print(f"{file_path} 文件已删除。")
    else:
        print(f"{file_path} 文件不存在。")

    spec = ["Spc"]
    #spec =[]
    for i in range(0,len(param_spect)):
       # spec.append(param_spect[i]/10000/100)#todo 校准数据除100
        spec.append(float(param_spect[i] /100))  # todo 校准数据除100

    # for i in range(0,50):
    #     spec.append(0.0)
    
    #print('spec:',len(spec),spec)
    
    # 数据准备
    data = [
        spec,
        ["X", param_name["CIE1931X,Y,Z"][0]],
        ["Y", param_name["CIE1931X,Y,Z"][1]],
        ["Z", param_name["CIE1931X,Y,Z"][2]],
        ["Ev", param_name["有效照度(lx)"]],
        ["u", param_name["坐标u\',v\'"][0]],
        ["v", param_name["坐标u\',v\'"][1]],
        ["x", param_name["坐标x,y"][0]],
        ["y", param_name["坐标x,y"][1]],
        ["T", param_name["相关色温(K)"]],
        ["duv", param_name["黑体偏离Duv"]],
        ["DW", 0],
        ["Pe", 0],
        # ["Re", 0],
        ["Re", float(param_name["R1-5"][0]), float(param_name["R1-5"][1]), float(param_name["R1-5"][2]), float(param_name["R1-5"][3]), float(param_name["R1-5"][4]), float(param_name["R6-10"][0]), float(param_name["R6-10"][1]), float(param_name["R6-10"][2]), float(param_name["R6-10"][3]), float(param_name["R6-10"][4]),
          float(param_name["R11-15"][0]), float(param_name["R11-15"][1]), float(param_name["R11-15"][2]), float(param_name["R11-15"][3]), float(param_name["R11-15"][4]), float(param_name["显色指数Ra"])],
        ["PW",0],
        ["Es", param_name["辐照度Ee(W/m)"]],
        ["SP", param_name["主波长(nm)"]],
    ]

    print('data:',data)

    # 写入 CSV 文件
    with open('Measurement.csv', mode='w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)

        # 写入数据
        writer.writerows(data)
    print("CSV 文件已成功写入！")

    return param_name, param_warelength, param_spect


"""----------------------------------------"""
"""-----------------T10A-------------------"""
"""----------------------------------------"""


# TODO t10
def online_mode_t10():
    print("T10A联机模式")
    send_data = "02 30 30 35 34 31 20 20 20 03 31 33 0D 0A"

    glo_ser.write(bytes.fromhex(send_data))
    time.sleep(0.5)
    read_byte_data_t10()
    time.sleep(0.5)


# 读取串口的值 1
def read_byte_data_t10():
    data_list = []
    while 1:
        rec_data = glo_ser.read()
        if rec_data == b'':
            break
        else:
            rec_byte = binascii.b2a_hex(rec_data)  # b'0x40'
            rec_byte = rec_byte.decode('utf-8')  # '40'
            data_list.append(rec_byte)
    return data_list


def revc_data_t10(count=1):
    if count == 3:
        time.sleep(5)
        print("2 T10检测EV 为0")

    if count >= 4:
        print("3 T10检测EV 为0")
        # exit()
        return 0

    send_data = "02 30 30 31 30 30 30 30 30 03 30 32 0D 0A"

    glo_ser.write(bytes.fromhex(send_data))
    time.sleep(0.1)
    data_list = read_byte_data_t10()
    if data_list[0] == "None":
        return "None"

    int_num = int(data_list[10][1] + data_list[11][1] + data_list[12][1] + data_list[13][1])

    mul = data_list[14][1]
    if mul == "0":
        int_num = int_num * 0.0001
    elif mul == "1":
        int_num = int_num * 0.001
    elif mul == "2":
        int_num = int_num * 0.01
    elif mul == "3":
        int_num = int_num * 0.1
    elif mul == "4":
        int_num = int_num * 1
    elif mul == "5":
        int_num = int_num * 10
    elif mul == "6":
        int_num = int_num * 100
    elif mul == "7":
        int_num = int_num * 1000
    elif mul == "8":
        int_num = int_num * 10000
    elif mul == "9":
        int_num = int_num * 100000
    if data_list[9] == "2B":
        pass
    elif data_list[9] == "2D":
        int_num = -int_num
    elif data_list[9] == "3D":
        int_num = 0

    if int(int_num) == 0:
        print("T10检测EV数据为0，重新检测")
        int_num = revc_data_t10(count=count+1)

    return int(int_num)

def cms_conntrol(com,cs,csv):
    bool=True
    try:
       # connect(com,cs)
        start_sampling("once")
        while True:
            time.sleep(0.5)
            res = read_sampling_status()
            # 一次测试已结束
            if res[3] == "01":
                break
            else:
                print("测试中...")
                time.sleep(0.2)

        read_test_result_data(csv)
    except Exception as e:
        bool=False
        print(e)
    finally:
        print(bool)
       # global glo_ser
      #  glo_ser.close()


if __name__ == '__main__':
    #connect("com3","CS-LM01IR")

    for i in range(1):
        cms_conntrol('com3', 'CS-LM01IR', 'Fiber.csv')
        time.sleep(0.1)

    # connect("com5","CS-LM01")
    # start_sampling("once")
    # while True:
    #     time.sleep(1)
    #     res = read_sampling_status()
    #     # 一次测试已结束
    #     if res[3] == "01":
    #         break
    #     else:
    #         print("测试中...")
    #         time.sleep(1)
    #
    # read_test_result_data('1.csv')
    # print(read_sampling_status())
    # read_light_lux_cct("CS-LM05")
    #
    # name,warelength,spect = read_test_result_data()

    # for i in range(100):
    # print(read_light_lux_cct("CS-LM01IR",True))
    # print(res,r)
    # time.sleep(1)
    # set_timer_mode("locking")
    # set_timer_mode("auto")
    # set_timer("1")






# # _*_ coding:utf-8 _*_
# # Group  :   ColorSpace
# # Author :   Chengyinhui
# # Data   :   2020/8/25
# # File   :   Spectrometer control V2.3.py
# # Tool   :   PyCharm
#
#
# import serial
# import binascii
# import time
# import re
# import struct
# import datetime
# import ctypes
# import threading
# import json
# import numpy as np
# import configparser
# import csv
# import os
#
#
# def listToJson(lst):
#     keys = [str(x) for x in np.arange(len(lst))]
#     list_json = dict(zip(keys, lst))
#     str_json = json.dumps(list_json, indent=2, ensure_ascii=False)  # json转为string
#     return str_json
#
#
# def connect_illuminometer(spec_num,com_number):  # connect sys ok error
#     print("连接",spec_num,com_number)
#     if spec_num == 1:
#         global ser1
#         ser1 = serial.Serial(com_number, 115200, bytesize=8, parity='N', stopbits=1, timeout=0.1)  # 230400
#         ser1.write(bytes.fromhex('01 10 FC BA 00 01 02 BA 01 32 65'))
#         recv_code = ser1.read(12).hex()
#         if len(recv_code) > 0:
#             return True
#         else:
#             return False
#
#
# def disconnect_illuminometer(num):
#     if num == 1:
#         ser1.close()
#
#
#
# def hex_to_float(h):
#     i = int(h, 16)
#     return struct.unpack('<f', struct.pack('<I', i))[0]
#
#
# def start_measure(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 10 FC B1 00 01 02 B1 01 34 2E'))
#         recv_code = ser1.read(12).hex()
#         print(recv_code)
#
#     # 判断是否测量完成
#     while True:
#         if spec_num == 1:
#             ser1.write(bytes.fromhex('01 03 FC B0 00 02 F5 BC'))
#             recv_code = ser1.read(12).hex()
#             time.sleep(0.1)
#             if recv_code[6:10] == "0101":
#                 # print("Measure Finish")
#                 Measure_time = str(datetime.datetime.now())[:-3]
#                 # print("measure time", Measure_time)
#                 return True
#             else:
#                 pass
#
# # EV
# def get_lux(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex(' 01 03 FC 12 00 02 54 5E'))
#         recv_code = ser1.read(12).hex()
#         lux_temp = str(recv_code[6:14])
#         return hex_to_float(lux_temp)
#
#
# # TCP
# def get_cct(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 03 FC 16 00 02 15 9F'))
#         recv_code = ser1.read(12).hex()
#         cct_temp = str(recv_code[6:14])
#         return hex_to_float(cct_temp)
#
# # x
# def get_cie_x(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 03 FC 1A 00 02 D5 9C'))
#         recv_code = ser1.read(12).hex()
#         CIE_x_temp = str(recv_code[6:14])
#         return hex_to_float(CIE_x_temp)
#
# # y
# def get_cie_y(spec_num):
#     pass
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 03 FC 1C 00 02 35 9D'))
#         recv_code = ser1.read(12).hex()
#         CIE_y_temp = str(recv_code[6:14])
#         return hex_to_float(CIE_y_temp)
#
#
# # dut 黑体偏离
# def get_Duv(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 03 FC 18 00 02 74 5C'))
#         recv_code = ser1.read(12).hex()
#         duv_temp = str(recv_code[6:14])
#         return hex_to_float(duv_temp)
#
#
# # ra 显色指数
# def get_Ra(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 03 FC 28 00 02 74 53'))
#         recv_code = ser1.read(12).hex()
#         duv_temp = str(recv_code[6:14])
#         return hex_to_float(duv_temp)
#
#
# # ra1 - ra 15 显色指数
# def get_Ra1_15(spec_num):
#     ra1_15_list = []
#     send_data_list = ["01 03 FC 2A 00 02 D5 93", "01 03 FC 2C 00 02 35 92",
#                       "01 03 FC 2E 00 02 94 52", "01 03 FC 30 00 02 F4 54",
#                       "01 03 FC 32 00 02 55 94", "01 03 FC 34 00 02 B5 95",
#                       "01 03 FC 36 00 02 14 55 ", "01 03 FC 38 00 02 75 96",
#                       "01 03 FC 3A 00 02 D4 56", "01 03 FC 3C 00 02 34 57",
#                       "01 03 FC 3E 00 02 95 97 ", "01 03 FC 40 00 02 F5 8F",
#                       "01 03 FC 42 00 02 54 4F", "01 03 FC 44 00 02 B4 4E",
#                       "01 03 FC 46 00 02 15 8E"
#                       ]
#     for i in range(15):
#         if spec_num == 1:
#             ser1.write(bytes.fromhex(send_data_list[i]))
#             recv_code = ser1.read(12).hex()
#
#         duv_temp = str(recv_code[6:14])
#         ra1_15_list.append(hex_to_float(duv_temp))
#
#     return ra1_15_list
#
#
# # 色差
# def get_CT(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 03 FC 26 00 02 15 90'))
#         recv_code = ser1.read(12).hex()
#         duv_temp = str(recv_code[6:14])
#         return hex_to_float(duv_temp)
#
# # u'
# def get_u_(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 03 FC 22 00 02 54 51'))
#         recv_code = ser1.read(12).hex()
#         duv_temp = str(recv_code[6:14])
#         return hex_to_float(duv_temp)
#
# # v'
# def get_v_(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 03 FC 24 00 02 B4 50'))
#         recv_code = ser1.read(12).hex()
#         duv_temp = str(recv_code[6:14])
#         return hex_to_float(duv_temp)
#
#
# # 主波长 FC4C ( Dominant Wavelength )
# def get_DW(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 03 FC 4C 00 02 35 8C'))
#         recv_code = ser1.read(12).hex()
#         CIE_y_temp = str(recv_code[6:14])
#         return hex_to_float(CIE_y_temp)
#
#
# # 纯度 FC4E ( Excitation Purity )
# def get_EP(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 03 FC 4E 00 02 94 4C'))
#         recv_code = ser1.read(12).hex()
#         CIE_y_temp = str(recv_code[6:14])
#         return hex_to_float(CIE_y_temp)
#
#
# # 明暗视觉比 S/P
# def get_SP(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 03 FC 4A 00 02 D5 8D'))
#         recv_code = ser1.read(12).hex()
#         CIE_y_temp = str(recv_code[6:14])
#         return hex_to_float(CIE_y_temp)
#
#
# def get_X(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 03 FC 5E 00 02 95 89'))
#         recv_code = ser1.read(12).hex()
#         CIE_y_temp = str(recv_code[6:14])
#         return hex_to_float(CIE_y_temp)
#
#
#
# def get_Y(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 03 FC 60 00 02 F4 45'))
#         recv_code = ser1.read(12).hex()
#         CIE_y_temp = str(recv_code[6:14])
#         return hex_to_float(CIE_y_temp)
#
#
#
# def get_Z(spec_num):
#     if spec_num == 1:
#         ser1.write(bytes.fromhex('01 03 FC 62 00 02 55 85'))
#         recv_code = ser1.read(12).hex()
#         CIE_y_temp = str(recv_code[6:14])
#         return hex_to_float(CIE_y_temp)
#
#
# def call_illuminometer(spec_num):
#     # return [200, 2500,
#     #         0.2,0.2,
#     #         0.3,0.3,
#     #         0.4,0.4,
#     #         0.5,0.5,
#     #         0.6,0.6,
#     #         ]
#     start_measure(spec_num)
#
#     ill_data = [float(round(get_lux(spec_num), 1)), float(round(get_cct(spec_num), 1)),
#                 float(round(get_cie_x(spec_num), 3)), float(round(get_cie_y(spec_num), 3)),
#                 float(round(get_u_(spec_num), 3)),float(round(get_v_(spec_num), 3)),
#                 float(round(get_Duv(spec_num), 2)), float(round(get_Ra(spec_num), 2)),
#                 float(round(get_CT(spec_num), 2)),  float(round(get_X(spec_num), 1)),
#                 float(round(get_Y(spec_num), 1)), float(round(get_Z(spec_num), 1))]
#    # print('ill_data:',ill_data)
#     return ill_data
#
#
# def illuminometer_anayse():
#     data = call_illuminometer(1)
#     #print("\n照度、色温",data)
#     return data
#
#
# # 获取 380 - 780 波段 光谱数据
# def get_Spectras(spec_num):
#     spectras_380_780_list = []
#
#     send_data_list = ["01 03 A0 00 00 63 27 E3 ", "01 03 A0 C8 00 63 A6 1D",
#                       "01 03 A1 90 00 63 26 32", "01 03 A2 58 00 63 A7 88",
#                       ]
#     for i in range(len(send_data_list)):
#         if spec_num == 1:
#             ser1.write(bytes.fromhex(send_data_list[i]))
#             recv_code = ser1.readall().hex()
#
#
#         result_100 = recv_code[6:]
#        # print(result_100)
#         if len(result_100) / 4 == 100.0:
#             for n in range(0, len(result_100), 4):
#                 sp = hex_to_float(result_100[n:n + 4])
#                 #print(sp)
#                 spectras_380_780_list.append(float(sp))
#     # for i in spectras_380_780_list:
#     #      print(i)
#   # #  print("spectras_380_780_list:",spectras_380_780_list)
#     return spectras_380_780_list
#
#
# if __name__ == '__main__':
#     connect_illuminometer(1,"COM12")
#    # for i in range(1):
#     print(illuminometer_anayse())
#     # ret = get_Spectras(1)
#     # print(len(ret))
#     # for i in ret:
#     #     print(i)
#     # 文件路径
#     file_path = 'output.csv'
#
#     # 判断文件是否存在
#     if os.path.exists(file_path):
#         os.remove(file_path)  # 删除文件
#         print(f"{file_path} 文件已删除。")
#     else:
#         print(f"{file_path} 文件不存在。")
#
#     # 数据准备
#     data = [
#         ["spc", 1, 2],
#         ["X", 789.404],
#         ["Y", 856.451],
#         ["Z", 803.975],
#         ["Ev",803.975],
#         ["u", 803.975],
#         ["x",  803.975],
#         ["y", 803.975],
#         ["T", 803.975],
#         ["duv", 803.975],
#         ["DW", 803.975],
#         ["Pe", 803.975],
#         ["Re", 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],
#         ["PW", 803.975],
#         ["Es", 803.975],
#         ["SP", 803.975],
#     ]
#
#     # 写入 CSV 文件
#     with open('output.csv', mode='w', newline='', encoding='utf-8') as file:
#         writer = csv.writer(file)
#
#         # 写入数据
#         writer.writerows(data)
#
#     print("CSV 文件已成功写入！")
#
#
#
#
#
#
