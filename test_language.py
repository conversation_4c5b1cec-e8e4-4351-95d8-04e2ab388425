#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试语言管理器功能
"""

from language_manager import language_manager

def test_language_manager():
    print("=== 测试语言管理器 ===")
    
    # 测试默认语言（中文）
    print(f"当前语言: {language_manager.get_current_language()}")
    print(f"连接: {language_manager.get_text('connect')}")
    print(f"断开: {language_manager.get_text('disconnect')}")
    print(f"开始: {language_manager.get_text('start')}")
    print(f"停止: {language_manager.get_text('stop')}")
    
    print("\n=== 切换到英文 ===")
    # 切换到英文
    language_manager.set_language('en')
    print(f"当前语言: {language_manager.get_current_language()}")
    print(f"连接: {language_manager.get_text('connect')}")
    print(f"断开: {language_manager.get_text('disconnect')}")
    print(f"开始: {language_manager.get_text('start')}")
    print(f"停止: {language_manager.get_text('stop')}")
    
    print("\n=== 切换回中文 ===")
    # 切换回中文
    language_manager.set_language('zh')
    print(f"当前语言: {language_manager.get_current_language()}")
    print(f"连接: {language_manager.get_text('connect')}")
    print(f"断开: {language_manager.get_text('disconnect')}")
    print(f"开始: {language_manager.get_text('start')}")
    print(f"停止: {language_manager.get_text('stop')}")

if __name__ == '__main__':
    test_language_manager()
