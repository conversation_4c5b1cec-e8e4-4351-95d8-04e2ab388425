# ColorSpace Multispectral 打包说明

## 问题解决

您遇到的 PyInstaller 打包错误主要是由于 scipy 和其他科学计算库的依赖问题导致的。我已经为您创建了完整的解决方案。

## 解决方案

### 1. 自动构建脚本

我创建了以下文件来解决依赖问题：

- `build_exe.py` - 主要的构建脚本（推荐使用）
- `build.bat` - Windows 批处理脚本
- `build_spec.py` - 高级构建脚本
- `hook-scipy.py` - scipy 依赖处理

### 2. 使用方法

#### 方法一：使用 Python 脚本（推荐）
```bash
python build_exe.py
```

#### 方法二：使用批处理文件
```bash
build.bat
```

### 3. 构建过程

脚本会自动执行以下步骤：

1. **清理旧文件** - 删除之前的构建文件
2. **安装依赖** - 确保所有必要的包都已安装
3. **构建可执行文件** - 使用 PyInstaller 打包

### 4. 解决的问题

- ✅ scipy 导入错误
- ✅ numpy 依赖问题  
- ✅ matplotlib 后端问题
- ✅ PyQt5 模块缺失
- ✅ 数据文件包含
- ✅ DLL 文件包含

### 5. 包含的数据文件

- `translations/` - 语言翻译文件
- `ledData/` - LED 数据
- `tempData/` - 温度数据
- `CIE_illminants/` - CIE 光源数据
- `data/` - 应用数据
- `Source/` - 资源文件
- `ui/` - 界面文件
- `*.dll`, `*.lib` - 动态链接库
- `*.csv`, `*.json` - 配置和数据文件

## 构建结果

成功构建后，您会在 `dist/` 目录中找到：
- `ColorSpace_Multispectral.exe` - 可执行文件

## 测试

运行测试脚本验证构建结果：
```bash
python test_exe.py
```

## 使用说明

### 系统要求
- Windows 7/8/10/11
- 至少 4GB 内存
- 至少 500MB 可用磁盘空间

### 部署
1. 将 `ColorSpace_Multispectral.exe` 复制到目标电脑
2. 双击运行即可，无需安装 Python 环境
3. 如果杀毒软件误报，请添加信任

### 注意事项
- 首次启动可能较慢（正在解压依赖）
- 确保有足够的临时磁盘空间
- 某些功能可能需要管理员权限

## 故障排除

### 常见问题

1. **启动失败**
   - 检查是否有杀毒软件阻止
   - 确保有足够的磁盘空间
   - 尝试以管理员身份运行

2. **缺少 DLL**
   - 安装 Visual C++ Redistributable
   - 确保所有 DLL 文件都在同一目录

3. **功能异常**
   - 检查数据文件是否完整
   - 确保配置文件存在

### 重新构建

如果需要重新构建：
```bash
# 清理
rmdir /s /q build dist
del *.spec

# 重新构建
python build_exe.py
```

## 技术细节

### PyInstaller 参数
- `--onefile` - 打包成单个文件
- `--windowed` - 隐藏控制台窗口
- `--hidden-import` - 手动指定隐藏导入
- `--collect-submodules` - 收集子模块
- `--add-data` - 添加数据文件

### 解决的依赖问题
- scipy.interpolate 相关模块
- numpy 随机数生成器
- matplotlib 后端
- PyQt5 UI 组件
- 串口通信模块

## 联系支持

如果遇到问题，请提供：
- 错误信息截图
- 系统版本信息
- 构建日志

---
*此文档由 Augment Agent 自动生成*
