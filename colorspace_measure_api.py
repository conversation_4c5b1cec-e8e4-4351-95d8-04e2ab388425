# _*_ coding:utf-8 _*_
# Group  :   ColorSpace
# Author :   <PERSON><PERSON>
# Data   :   2022/05/01
# Tool   :   PyCharm


import time
import serial
import binascii
import struct

base = [str(x) for x in range(10)] + [chr(x) for x in range(ord('A'), ord('A') + 6)]


def dec2hex(num):
    l = []
    if num < 0:
        return '-' + dec2hex(abs(num))
    while True:
        num, rem = divmod(num, 16)
        l.append(base[rem])
        if num == 0:
            return ''.join(l[::-1])


def hex_data(data):
    result_data = ""

    if len(dec2hex(int(data))) == 1:
        result_data = "0000000" + dec2hex(int(data))
    if len(dec2hex(int(data))) == 2:
        result_data = "000000" + dec2hex(int(data))
    if len(dec2hex(int(data))) == 3:
        result_data = "00000" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 4:
        result_data = "0000" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 5:
        result_data = "000" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 6:
        result_data = "00" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 7:
        result_data = "0" + dec2hex(int(data))
    elif len(dec2hex(int(data))) == 8:
        result_data = dec2hex(int(data))
    res = ""
    for i in range(0, len(result_data) - 1, 2):
        res = result_data[i] + result_data[i + 1] + " " + res
    return res


def connect(com_number="com4", type="normal"):  # 连接光源
    global glo_ser
    global test_type
    test_type = "None"

    if type == "T10-A":
        glo_ser = serial.Serial(com_number, 9600, bytesize=7, parity='E', stopbits=1, timeout=0.01)  # 230400
        online_mode_t10()
    elif type == "CS-LM01IR":
        test_type = "CS-LM01IR"
        glo_ser = serial.Serial(com_number, 115200, bytesize=8, parity='N', stopbits=1, timeout=0.1)  # 230400
        online_mode()
    else:
        glo_ser = serial.Serial(com_number, 9600, bytesize=8, parity='N', stopbits=1, timeout=0.1)  # 230400
        online_mode()


def disconnect():  # 连接光源
    global glo_ser

    glo_ser.close()


# 联机模式 1
def online_mode():
    if test_type == "None":
        send_data = "8C 00"
    else:
        send_data = "25 00"

    glo_ser.write(bytes.fromhex(send_data))


# 读取串口的值 1
def read_byte_data():
    data_list = []
    while True:

        rec_data = glo_ser.read(1)
        if rec_data != b'':
            pass
        else:
            break

        rec_byte = binascii.b2a_hex(rec_data)  # b'0x40'

        rec_str = rec_byte.decode('utf-8')  # '40'

        data_list.append(rec_str)
    return data_list


# 读采样状态 1
def read_sampling_status():
    if test_type == "None":
        send_data = "8C 03"
    else:
        send_data = "25 03"

    glo_ser.write(bytes.fromhex(send_data))

    data_list = read_byte_data()

    return data_list


# 设置积分模式
def set_timer_mode(cmd=""):
    # 自动
    if test_type == "None":
        if cmd == "auto":
            send_data = "8C 02 01"
        # 锁定
        if cmd == "locking":
            send_data = "8C 02 00"
    else:
        if cmd == "auto":
            send_data = "25 02 01"
        # 锁定
        if cmd == "locking":
            send_data = "25 02 00"
    glo_ser.write(bytes.fromhex(send_data))


# 设置积分时间 ms 2 ,
def set_timer(time="2"):
    time = str(int(time) * 1000)  # 原来单位us

    res = hex_data(time)

    if test_type == "None":
        send_data = "8c 01 " + res
    else:
        send_data = "25 01 " + res

    glo_ser.write(bytes.fromhex(send_data))


# 读波长参数命令
def read_wavelength_param():
    send_data = "8C 0B"

    glo_ser.write(bytes.fromhex(send_data))


# 读光谱标定数据
def read_calibrate_data():
    send_data = "8C 0C"

    glo_ser.write(bytes.fromhex(send_data))


# 写光谱标定数据
def write_calibrate_data():
    send_data = "8C 0D" + "write"

    glo_ser.write(bytes.fromhex(send_data))


# 开始采样命令
def start_sampling(cmd):
    """
    :param cmd:  00 :即停止测量。  01 :即单次测量。  02:即连续测量
    :return:
    """
    send_data = None
    if test_type == "None":
        if cmd == "stop":
            send_data = "8C 25"
        elif cmd == "once":
            send_data = "8C 0E 01"
        elif cmd == "continues":
            send_data = "8C 0E 02"
    else:
        if cmd == "stop":
            send_data = "25 25"
        elif cmd == "once":
            send_data = "25 0E 01"
        elif cmd == "continues":
            send_data = "25 0E 02"

    glo_ser.write(bytes.fromhex(send_data))


# 数据反转
def analyze_data(data):
    res = data[::-1]

    res_list = []
    for i in res:
        # 16 进制转10进制
        c = int(i, 16)
        res_list.append(c)

    s = struct.unpack(">f", bytes(res_list))[0]
    return s


def get_date():
    curtime = time.strftime('%Y-%m-%d ', time.localtime(time.time()))
    return curtime


def get_time():
    curtime = time.strftime('%H:%M:%S ', time.localtime(time.time()))
    return curtime


def read_light_lux_cct(devices_type="",ir_test_mode = False):
    if test_type == "None":
        send_data = "8C 13"
    else:
        send_data = "25 13"

    lux,cct,x,y,z = 0,0,0,0,0

    glo_ser.write(bytes.fromhex(send_data))

    data_list = read_byte_data()

    if devices_type == "CS-LM-01LR":
        lux = int(analyze_data(data_list[40:44]))  # 显示屏开始第一个 40
        cct = int(analyze_data(data_list[48:52]))

    elif devices_type == "CS-LM01IR":
        # 测试红外
        if ir_test_mode:
            lux = round(analyze_data(data_list[156:160]), 4) * 1000
        else:
            lux = int(analyze_data(data_list[40:44]))
        cct = int(analyze_data(data_list[48:52]))
        x = int(analyze_data(data_list[200:204]))
        y = int(analyze_data(data_list[204:208]))
        z = int(analyze_data(data_list[208:212]))

    elif devices_type == "CS-LS-03":
        lux = int(analyze_data(data_list[72:76]))
        cct = int(analyze_data(data_list[80:84]))

    elif devices_type == "CS-LM05":

        lux = int(analyze_data(data_list[56:60]))
        cct = int(analyze_data(data_list[64:68]))
        x = int(analyze_data(data_list[216:220]))
        y = int(analyze_data(data_list[220:224]))
        z = int(analyze_data(data_list[224:228]))


    if lux > 2000000 or lux <0:
        print("照度 > 200000")
        lux = 0

    if cct > 1000000 or cct <0:
        print("色温 > 100000")
        cct = 0

    return lux,cct,x,y,z


# 读测试结果数据命令
def read_test_result_data():
    if test_type == "None":
        send_data = "8C 13"
    else:
        send_data = "25 13"

    glo_ser.write(bytes.fromhex(send_data))

    data_list = read_byte_data()

    param_name = {
        '闪烁频率(Hz)': "",
        '波动深度(%)': "",
        '闪烁百分比(%)': "",
        '闪烁指数': "",
        '有效照度(lx)': "",
        '峰值照度(lx)': "",
        '照度积分(lx.s)': "",
        '闪光时间(ms)': "",
        '照度E(lx)': "",
        '烛光E(fc)': "",
        '相关色温(K)': "",
        '黑体偏离Duv': "",
        '坐标x,y': "",
        '坐标u,v': "",
        '坐标u\',v\'': "",
        '坐标y0,dy': "",
        '色容差SDCM': "",
        '显色指数Ra': "",
        'R1-5': "",
        'R6-10': "",
        'R11-15': "",
        '辐照度Ee(W/m)': "",
        '明暗视觉比S/P': "",
        '主波长(nm)': "",
        '色纯度(%)': "",
        '半宽度(nm)': "",
        '峰值波(nm)': "",
        '中心波长(nm)': "",
        '质心波长(nm)': "",
        '红色比(%)': "",
        '绿色比(%)': "",
        '蓝色比(%)': "",
        'CIE1931X,Y,Z': "",
        'TLCI-2012': "",
        '积分时间(us)': "",
        '峰值信号': "",
        '暗信号': "",
        '补偿电平': "",
        '起始波长(nm)': "",
        '终止波长(nm)': "",
        '测试日期': get_date(),
        '测试时间': get_time(),

    }

    param_name["闪烁频率(Hz)"] = round(analyze_data(data_list[40:44]), 1)
    param_name["波动深度(%)"] = round(analyze_data(data_list[44:48]), 1)
    param_name["闪烁百分比(%)"] = round(analyze_data(data_list[48:52]), 1)
    param_name["闪烁指数"] = round(analyze_data(data_list[52:56]), 2)
    param_name["有效照度(lx)"] = round(analyze_data(data_list[56:60]), 2)
    param_name["峰值照度(lx)"] = round(analyze_data(data_list[60:64]), 1)
    param_name["照度积分(lx.s)"] = round(analyze_data(data_list[64:68]), 1)
    param_name["闪光时间(ms)"] = round(analyze_data(data_list[68:72]), 2)
    param_name["照度E(lx)"] = round(analyze_data(data_list[72:76]), 1)
    param_name["烛光E(fc)"] = round(analyze_data(data_list[76:80]), 2)
    param_name["相关色温(K)"] = int(analyze_data(data_list[80:84]))
    param_name["黑体偏离Duv"] = round(analyze_data(data_list[84:88]), 5)
    param_name["坐标x,y"] = round(analyze_data(data_list[88:92]), 4), round(analyze_data(data_list[92:96]), 4)
    param_name["坐标u,v"] = round(analyze_data(data_list[96:100]), 4), round(analyze_data(data_list[100:104]), 4)
    param_name["坐标u\',v\'"] = round(analyze_data(data_list[104:108]), 4), round(analyze_data(data_list[108:112]), 4)
    param_name["坐标y0,dy"] = round(analyze_data(data_list[112:116]), 4), round(analyze_data(data_list[116:120]), 4)
    param_name["色容差SDCM"] = round(analyze_data(data_list[120:124]), 2)
    param_name["显色指数Ra"] = round(analyze_data(data_list[124:128]), 1)
    param_name["R1-5"] = round(analyze_data(data_list[124:128]), 0), round(analyze_data(data_list[128:132]), 0), round(
        analyze_data(data_list[132:136]), 0), \
                         round(analyze_data(data_list[136:140]), 0), round(analyze_data(data_list[140:144]), 0)

    param_name["R6-10"] = round(analyze_data(data_list[144:148]), 0), round(analyze_data(data_list[148:152]), 0), \
                          round(analyze_data(data_list[152:156]), 0), round(analyze_data(data_list[156:160]), 0), \
                          round(analyze_data(data_list[160:164]), 0)

    param_name["R11-15"] = round(analyze_data(data_list[164:168]), 0), round(analyze_data(data_list[168:172]), 0), \
                           round(analyze_data(data_list[172:176]), 0), round(analyze_data(data_list[176:180]), 0), \
                           round(analyze_data(data_list[180:184]), 0)
    param_name["辐照度Ee(W/m)"] = round(analyze_data(data_list[188:192]), 4) * 1000
    param_name["明暗视觉比S/P"] = round(analyze_data(data_list[192:196]), 3)
    param_name["主波长(nm)"] = round(analyze_data(data_list[196:200]), 2)
    param_name["色纯度(%)"] = round(analyze_data(data_list[200:204]), 1)

    param_name["半宽度(nm)"] = round(analyze_data(data_list[204:208]), 1)
    param_name["峰值波(nm)"] = round(analyze_data(data_list[208:212]), 1)
    param_name["中心波长(nm)"] = round(analyze_data(data_list[212:216]), 1)
    param_name["质心波长(nm)"] = round(analyze_data(data_list[216:220]), 1)
    param_name["红色比(%)"] = round(analyze_data(data_list[220:224]), 1)
    param_name["绿色比(%)"] = round(analyze_data(data_list[224:228]), 1)
    param_name["蓝色比(%)"] = round(analyze_data(data_list[228:232]), 1)

    param_name["CIE1931X,Y,Z"] = round(analyze_data(data_list[232:236]), 3), round(analyze_data(data_list[232:236]), 3), \
                                 round(analyze_data(data_list[236:240]), 3)

    param_name["TLCI-2012"] = round(analyze_data(data_list[244:248]), 0)
    param_name["积分时间(us)"] = round(analyze_data(data_list[248:252]), 2) / 1000
    param_name["峰值信号"] = round(analyze_data(data_list[252:256]), 0)
    param_name["暗信号"] = round(analyze_data(data_list[256:260]), 0)
    param_name["补偿电平"] = round(analyze_data(data_list[260:264]), 4)

    param_name["起始波长(nm)"] = int(analyze_data(data_list[-8:-4]))
    param_name["终止波长(nm)"] = int(analyze_data(data_list[-4:]))
    start = (param_name["起始波长(nm)"])
    end = (param_name["终止波长(nm)"])
    # 光谱数据
    param_spect = {}
    n = 0
    for i in range(start, end + 1):
        param_spect["光谱{}".format(i)] = int(analyze_data(data_list[284 + n:288 + n]) / 10 * 10000) / 10000
        n += 4

    # 波长数据
    param_warelength = {}
    for i in range(start, end + 1):
        param_warelength["波长{}".format(i)] = i
    # print(len(param_name))
    # for i in param_name:
    #     print(i,param_name[i])
    #
    # for i in param_spect:
    #     print(i,param_spect[i])

    # for i in param_warelength:
    #     print(i,param_warelength[i])

    return param_name, param_warelength, param_spect


"""----------------------------------------"""
"""-----------------T10A-------------------"""
"""----------------------------------------"""


# TODO t10
def online_mode_t10():
    print("T10A联机模式")
    send_data = "02 30 30 35 34 31 20 20 20 03 31 33 0D 0A"

    glo_ser.write(bytes.fromhex(send_data))
    time.sleep(0.5)
    read_byte_data_t10()
    time.sleep(0.5)


# 读取串口的值 1
def read_byte_data_t10():
    data_list = []

    while 1:

        rec_data = glo_ser.read()

        if rec_data == b'':
            break
        else:

            rec_byte = binascii.b2a_hex(rec_data)  # b'0x40'

            rec_byte = rec_byte.decode('utf-8')  # '40'

            data_list.append(rec_byte)

    return data_list


def revc_data_t10(count=1):
    if count == 3:
        time.sleep(5)
        print("2 T10检测EV 为0")

    if count >= 4:
        print("3 T10检测EV 为0")
        # exit()
        return 0

    send_data = "02 30 30 31 30 30 30 30 30 03 30 32 0D 0A"

    glo_ser.write(bytes.fromhex(send_data))
    time.sleep(0.1)
    data_list = read_byte_data_t10()
    if data_list[0] == "None":
        return "None"

    int_num = int(data_list[10][1] + data_list[11][1] + data_list[12][1] + data_list[13][1])

    mul = data_list[14][1]
    if mul == "0":
        int_num = int_num * 0.0001
    elif mul == "1":
        int_num = int_num * 0.001
    elif mul == "2":
        int_num = int_num * 0.01
    elif mul == "3":
        int_num = int_num * 0.1
    elif mul == "4":
        int_num = int_num * 1
    elif mul == "5":
        int_num = int_num * 10
    elif mul == "6":
        int_num = int_num * 100
    elif mul == "7":
        int_num = int_num * 1000
    elif mul == "8":
        int_num = int_num * 10000
    elif mul == "9":
        int_num = int_num * 100000
    if data_list[9] == "2B":
        pass
    elif data_list[9] == "2D":
        int_num = -int_num
    elif data_list[9] == "3D":
        int_num = 0

    if int(int_num) == 0:
        print("T10检测EV数据为0，重新检测")
        int_num = revc_data_t10(count=count+1)

    return int(int_num)


if __name__ == '__main__':
    connect("com12","CS-LM01IR")
    start_sampling("once")
    while True:
        time.sleep(1)
        res = read_sampling_status()
        # 一次测试已结束
        if res[3] == "01":
            break
        else:
            print("测试中...")
            time.sleep(1)

    read_light_lux_cct("CS-LM01IR",False)
    #print(read_light_lux_cct("CS-LM05",True))

    # print(read_sampling_status())
    # read_light_lux_cct("CS-LM05")
    #
    # name,warelength,spect = read_test_result_data()

    # for i in range(100):
    # print(read_light_lux_cct("CS-LM01IR",True))
    # print(res,r)
    # time.sleep(1)
    # set_timer_mode("locking")
    # set_timer_mode("auto")

    # set_timer("1")