# _*_ coding:utf-8 _*_
# Group  :   ColorSpace
# Author :   <PERSON><PERSON><PERSON><PERSON>
# Data   :   2019/10/26
# File   :   main.py
# Tool   :   PyCharm


import numpy as np
#import matplotlib.pylab as plt
import xlrd


# 将表格数据读取到[] 照度
cl500a_illum_array = []
iso_light_illum_array_1 = []
iso_light_illum_array_2 = []
iso_light_illum_array_3 = []
iso_light_illum_array_4 = []

# # 将表格数据读取到[] 色温
cl500a_cct_array = []
iso_light_cct_array_1 = []
iso_light_cct_array_2 = []
iso_light_cct_array_3 = []
iso_light_cct_array_4 = []


'''
# 画出原来x,y对应关系
plt.plot(x,y,label="ori")
# 画出拟合函数f转换后的数据
plt.plot(x,f(x),label="fit")
plt.legend()
plt.show()
'''


def read_ill_data(inpath,num,type):
    data = xlrd.open_workbook(inpath, encoding_override='utf-8')
    table = data.sheets()[0]
    nrows = table.nrows
    ncols = table.ncols
    # list1 =[]
    for i in range(1, nrows):
        #print("huawei:",result)
        if type == "CL500A":
            alldata = table.row_values(i)
            result = alldata[num]
            cl500a_illum_array.append(float(result))
        elif type == "Iso Light1":
            alldata = table.row_values(i)
            result = alldata[num]
            iso_light_illum_array_1.append(float(result))
        elif type == "Iso Light2":
            alldata = table.row_values(i)
            result = alldata[num]
            iso_light_illum_array_2.append(float(result))
        elif type == "Iso Light3":
            alldata = table.row_values(i)
            result = alldata[num]
            iso_light_illum_array_3.append(float(result))
        elif type == "Iso Light4":
            alldata = table.row_values(i)
            result = alldata[num]
            iso_light_illum_array_4.append(float(result))


def read_cct_data(inpath,num,type):
    data = xlrd.open_workbook(inpath, encoding_override='utf-8')
    table = data.sheets()[0]
    nrows = table.nrows
    ncols = table.ncols
    # list1 =[]
    for i in range(1, nrows):
        #print("huawei:",result)
        if type == "CL500A":
            alldata = table.row_values(i)
            result = alldata[num]
            if result == "":
                pass
            else:
                cl500a_cct_array.append(float(result))
                #print("read 500a TCP")
        elif type == "Iso Light1 Tcp":
            alldata = table.row_values(i)
            result = alldata[num]
            if result == "":
                pass
            else:
                iso_light_cct_array_1.append(float(result))
        # elif type == "Iso Light2 Tcp":
        #     alldata = table.row_values(i)
        #     result = alldata[num]
        #     if result == "":
        #         pass
        #     else:
        #         iso_light_cct_array_2.append(float(result))
        # elif type == "Iso Light3 Tcp":
        #     alldata = table.row_values(i)
        #     result = alldata[num]
        #     if result == "":
        #         pass
        #     else:
        #         iso_light_cct_array_3.append(float(result))
        # elif type == "Iso Light4 Tcp":
        #     alldata = table.row_values(i)
        #     result = alldata[num]
        #     if result == "":
        #         pass
        #     else:
        #         iso_light_cct_array_4.append(float(result))
   # print(cl500a_cct_array)


def get_iso_light_puck_illum(type,num):
    cl500a_illum_array.clear()
    iso_light_illum_array_1.clear()
    iso_light_illum_array_2.clear()
    iso_light_illum_array_3.clear()
    iso_light_illum_array_4.clear()
    if type == 1:
        read_ill_data("data/illum_data.xlsx", 1, "CL500A")
        read_ill_data("data/illum_data.xlsx", 2, "Iso Light1")
        # 其他数据  ISO Light
        x = np.array(iso_light_illum_array_1)
        # print("X",x)
        # 500A数据
        y = np.array(cl500a_illum_array)
        # print("Y",y)
        # 打印其中一个数据
        # 线性拟合，数字1为拟合多项式次数，1：y=ax+b  2:y=ax^2 + bx + c ...
        # p:[a,b,c...] 多项式系数
        p = np.polyfit(x, y, 2)
        # f:function,直接应用y'=f(x)
        f = np.poly1d(p)
       # print(int(f(num)))
        return int(f(num))
    # elif type == 2:
    #     read_ill_data("data/cali_ill_data.xlsx", 1, "CL500A")
    #     read_ill_data("data/cali_ill_data.xlsx", 3, "Iso Light2")
    #     # 其他数据  ISO Light
    #     x2 = np.array(iso_light_illum_array_2)
    #     #print("X",x2)
    #     # 500A数据
    #     y2 = np.array(cl500a_illum_array)
    #     #print("Y",y2)
    #     # 打印其中一个数据
    #     # 线性拟合，数字1为拟合多项式次数，1：y=ax+b  2:y=ax^2 + bx + c ...
    #     # p:[a,b,c...] 多项式系数
    #    # print("---0-------")
    #     p2 = np.polyfit(x2, y2, 2)
    #    # print("---1-------")
    #     # f:function,直接应用y'=f(x)
    #     f = np.poly1d(p2)
    #     # print(int(f(num)))
    #     #print("-----2-----")
    #     return int(f(num))
    # elif type == 3:
    #     read_ill_data("data/cali_ill_data.xlsx", 1, "CL500A")
    #     read_ill_data("data/cali_ill_data.xlsx", 4, "Iso Light3")
    #     # 其他数据  ISO Light
    #     x3 = np.array(iso_light_illum_array_3)
    #     # print("X",x)
    #     # 500A数据
    #     y3 = np.array(cl500a_illum_array)
    #     # print("Y",y)
    #     # 打印其中一个数据
    #     # 线性拟合，数字1为拟合多项式次数，1：y=ax+b  2:y=ax^2 + bx + c ...
    #     # p:[a,b,c...] 多项式系数
    #     p = np.polyfit(x3, y3, 2)
    #     # f:function,直接应用y'=f(x)
    #     f = np.poly1d(p)
    #     # print(int(f(num)))
    #     return int(f(num))
    # elif type == 4:
    #     read_ill_data("data/cali_ill_data.xlsx", 1, "CL500A")
    #     read_ill_data("data/cali_ill_data.xlsx", 5, "Iso Light4")
    #     # 其他数据  ISO Light
    #     x4 = np.array(iso_light_illum_array_4)
    #     # print("X",x)
    #     # 500A数据
    #     y4 = np.array(cl500a_illum_array)
    #     # print("Y",y)
    #     # 打印其中一个数据
    #     # 线性拟合，数字1为拟合多项式次数，1：y=ax+b  2:y=ax^2 + bx + c ...
    #     # p:[a,b,c...] 多项式系数
    #     p = np.polyfit(x4, y4, 2)
    #     # f:function,直接应用y'=f(x)
    #     f = np.poly1d(p)
    #     # print(int(f(num)))
    #     return int(f(num))


def get_iso_light_puck_cct(type,num):
    cl500a_cct_array.clear()
    iso_light_cct_array_1.clear()
    # iso_light_cct_array_2.clear()
    # iso_light_cct_array_3.clear()
    # iso_light_cct_array_4.clear()
    if type == 1:
       # print("iso puck 1")
        read_cct_data("data/cct_data.xlsx", 1, "CL500A")
        read_cct_data("data/cct_data.xlsx", 2, "Iso Light1 Tcp")
        # 其他数据  ISO Light
        x = np.array(iso_light_cct_array_1)
        #print("x:",x)
        # 500A数据
        y = np.array(cl500a_cct_array)
        #print("y:",y)
        # print("Y",y)
        # 打印其中一个数据
        # 线性拟合，数字1为拟合多项式次数，1：y=ax+b  2:y=ax^2 + bx + c ...
        # p:[a,b,c...] 多项式系数
        p = np.polyfit(x, y, 2)
        # f:function,直接应用y'=f(x)
        f = np.poly1d(p)
        #print(int(f(num)))
        return int(f(num))


if __name__ == '__main__':
    #read_data("iso_light_data.xlsx", 1, "CL500A")
    print(get_iso_light_puck_illum(1,3000))
   # print(get_iso_light_puck_cct(1,7721))
    #print(get_iso_light_puck_cct(1,5678))
   # read_cct_data("cali_cct_data.xlsx", 0, "CL500A")