# 多光谱控制软件 - 中英文切换功能说明

## 功能概述

本软件已成功添加中英文切换功能，用户可以在运行时动态切换界面语言，无需重启软件。

## 使用方法

### 1. 启动软件
运行 `main.py` 启动软件，默认语言为中文。

### 2. 切换语言
在软件主界面的菜单栏中：
- 点击 "语言" 菜单
- 选择 "中文" 或 "English" 来切换语言

### 3. 支持的语言
- **中文 (zh)**: 默认语言
- **English (en)**: 英文界面

## 技术实现

### 文件结构
```
├── main.py                    # 主程序文件
├── language_manager.py        # 语言管理器
├── translations/              # 翻译文件目录
│   ├── zh.json               # 中文翻译
│   └── en.json               # 英文翻译
└── test_language.py          # 语言功能测试脚本
```

### 核心组件

#### 1. 语言管理器 (language_manager.py)
- `LanguageManager` 类：负责管理语言切换和翻译文本
- `load_translations()`: 加载翻译文件
- `set_language()`: 设置当前语言
- `get_text()`: 获取翻译文本

#### 2. 翻译文件 (translations/*.json)
- JSON格式存储翻译文本
- 支持嵌套结构和特殊字符
- 易于维护和扩展

#### 3. 主程序集成
- `init_language_menu()`: 初始化语言菜单
- `change_language()`: 切换语言处理
- `apply_language_settings()`: 应用语言设置到UI
- `update_button_texts()`: 更新按钮文本
- `update_label_texts()`: 更新标签文本
- `update_groupbox_texts()`: 更新组框文本

## 已翻译的界面元素

### 按钮
- 连接/断开 (Connect/Disconnect)
- 开始/停止 (Start/Stop)
- 保存/导入 (Save/Import)
- 测量/匹配 (Measure/Match)
- 校准相关按钮 (Calibration buttons)

### 标签和文本
- 窗口标题
- 连接状态信息
- 校准完成提示
- 错误和警告消息
- 表格标题

### 菜单
- 语言切换菜单
- 设置选项

## 扩展翻译

### 添加新的翻译文本
1. 在 `translations/zh.json` 中添加中文文本
2. 在 `translations/en.json` 中添加对应的英文文本
3. 在代码中使用 `language_manager.get_text('key')` 获取翻译

### 示例
```json
// zh.json
{
  "new_button": "新按钮"
}

// en.json  
{
  "new_button": "New Button"
}
```

```python
# 在代码中使用
button.setText(language_manager.get_text('new_button'))
```

## 测试

运行测试脚本验证语言切换功能：
```bash
python test_language.py
```

## 注意事项

1. **字符编码**: 所有文件使用UTF-8编码，确保中文字符正确显示
2. **翻译文件**: 修改翻译文件后需要重启软件才能生效
3. **UI更新**: 语言切换会立即更新界面，无需重启
4. **扩展性**: 可以轻松添加更多语言支持

## 未来改进

1. 添加更多语言支持（如日语、韩语等）
2. 实现翻译文件热重载
3. 添加语言设置持久化存储
4. 完善更多UI元素的翻译覆盖

## 版本信息

- 功能添加日期: 2025-07-30
- 支持的PyQt版本: PyQt5
- 兼容性: Windows 系统
