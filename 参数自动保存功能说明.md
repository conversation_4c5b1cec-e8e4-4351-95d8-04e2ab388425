# 光谱检测软件 - 参数自动保存功能说明

## 功能概述

本软件已成功添加参数自动保存功能，用户在主界面中设置的参数（如串口选择、通道选择、协议设置等）会自动保存到配置文件中，下次启动软件时会自动加载这些参数，无需重新设置。

## 功能特点

### 1. 自动保存参数
- 用户在界面上更改任何参数时，系统会自动保存到 `setting.json` 文件
- 无需手动保存操作，实时同步设置
- 支持多种参数类型的保存

### 2. 自动加载参数
- 软件启动时自动从配置文件加载之前的设置
- 界面控件会自动恢复到上次的状态
- 确保用户体验的连续性

### 3. 参数持久化
- 所有设置永久保存在 `setting.json` 文件中
- 即使软件异常退出，设置也不会丢失
- 支持手动编辑配置文件

## 支持的参数类型

### 串口设置
- **光源串口**: 6个光源的串口选择（light_com1 ~ light_com6）
- **光谱仪串口**: 光谱仪设备串口选择（spec_com）
- **测量设备串口**: 测量设备串口选择（measure_com）

### 设备设置
- **协议类型**: 通信协议选择（current_protocol_type）
- **测量设备类型**: 测量设备类型选择（measure_device_type）

### 通道设置
- **通道启用状态**: 6个通道的启用/禁用状态（ch1_enabled ~ ch6_enabled）
- **通道数量**: 从现有的 channels_num 参数读取

## 使用方法

### 1. 设置参数
1. 启动软件，界面会自动加载之前保存的参数
2. 在主界面中修改任何参数（串口选择、协议选择等）
3. 参数会自动保存，无需手动操作

### 2. 查看保存的参数
- 打开 `setting.json` 文件可以查看所有保存的参数
- 参数以JSON格式存储，便于阅读和编辑

### 3. 重置参数
- 如需重置参数，可以删除 `setting.json` 文件
- 软件会使用默认设置重新创建配置文件

## 技术实现

### 核心方法

#### 1. 参数保存
```python
def save_setting(self):
    """保存当前设置到文件"""
    with open("setting.json", 'w', encoding='utf-8') as f:
        json.dump(self.setting, f, ensure_ascii=False, indent=2)

def update_setting(self, key, value):
    """更新设置并自动保存"""
    self.setting[key] = value
    self.save_setting()
```

#### 2. 参数加载
```python
def load_serial_settings(self):
    """加载串口设置"""
    # 加载各种串口设置到界面控件

def load_ui_settings(self):
    """加载其他界面设置"""
    # 加载协议、设备类型、复选框状态等
```

#### 3. 自动监听
```python
def init_auto_save_settings(self):
    """初始化参数自动保存功能"""
    # 监听界面控件变化并自动保存
    self.ui.comboBox_serials.currentTextChanged.connect(
        lambda text: self.update_setting("light_com1", text))
```

### 配置文件格式
```json
{
  "channels_num": 31,
  "light_com1": "COM9",
  "light_com2": "COM19",
  "light_com3": "COM20",
  "light_com4": "COM21",
  "light_com5": "COM22",
  "light_com6": "COM23",
  "spec_com": "COM1",
  "measure_com": "COM2",
  "current_protocol_type": "0",
  "measure_device_type": "CS-LM01",
  "ch1_enabled": true,
  "ch2_enabled": false,
  "ch3_enabled": false,
  "ch4_enabled": false,
  "ch5_enabled": false,
  "ch6_enabled": false,
  "protocol": {
    "0": "01 00 00",
    "1": "01 00 01",
    "2": "01 00 02",
    "3": "01 00 03",
    "4": "01 00 04"
  },
  "std_data": {
    // 标准数据...
  }
}
```

## 监听的界面控件

### 串口选择控件
- `comboBox_serials` → `light_com1`
- `comboBox_serials_2` → `light_com2`
- `comboBox_serials_3` → `light_com3`
- `comboBox_serials_4` → `light_com4`
- `comboBox_serials_5` → `light_com5`
- `comboBox_serials_6` → `light_com6`
- `spec_comboBox_serials` → `spec_com`
- `comboBox_cs_lm01` → `measure_com`
- `comboBox_serials_Le007` → `le007_com`

### 设置选择控件
- `comboBox_protocol` → `current_protocol_type`
- `measure_comboBox` → `measure_device_type`
- `comboBox_ledData` → `selected_led_data`
- `comboBox_MatchMethod` → `match_method`

### 复选框控件
- `checkBox_ch1` → `ch1_enabled`
- `checkBox_ch2` → `ch2_enabled`
- `checkBox_ch3` → `ch3_enabled`
- `checkBox_ch4` → `ch4_enabled`
- `checkBox_ch5` → `ch5_enabled`
- `checkBox_ch6` → `ch6_enabled`

### 路径设置控件
- `lineEdit_MatchSpdPath` → `match_spd_path`
- `lineEdit_correctionSavePath` → `correction_save_path`

### 数值设置控件
- `spinBox_SpdAccuracy` → `spd_accuracy`
- `spinBox_tagetLuminance` → `target_luminance`
- `tl84_spinBox` → `tl84_value`
- `cwf_spinBox` → `cwf_value`
- `flick_spinBox` → `flick_value`
- `spinBox_le007_left_illum` → `le007_left_illum`

## 优势特点

1. **用户友好**: 无需手动保存，自动记忆用户设置
2. **实时同步**: 参数更改立即保存，不会丢失
3. **向后兼容**: 兼容现有的配置文件格式
4. **扩展性强**: 可以轻松添加新的参数监听
5. **稳定可靠**: 异常处理确保程序稳定运行

## 注意事项

1. **文件权限**: 确保程序有写入 `setting.json` 文件的权限
2. **配置文件**: 不要手动删除配置文件中的必要字段
3. **串口设备**: 串口参数保存的是串口名称，需要确保设备连接正确
4. **参数验证**: 系统会自动验证参数有效性，无效参数会使用默认值

## 故障排除

### 常见问题
1. **参数不保存**: 检查程序是否有写入权限
2. **参数不加载**: 检查 `setting.json` 文件格式是否正确
3. **串口不匹配**: 确保保存的串口名称与实际设备匹配

### 调试方法
1. 查看控制台输出的保存/加载信息
2. 检查 `setting.json` 文件内容
3. 确认界面控件的信号连接是否正常

## 扩展功能

### 添加新的参数监听
1. 在 `init_auto_save_settings()` 方法中添加新的信号连接
2. 在 `load_ui_settings()` 方法中添加参数加载逻辑
3. 确保配置文件中包含对应的默认值

### 示例代码
```python
# 添加新的参数监听
self.ui.new_comboBox.currentTextChanged.connect(
    lambda text: self.update_setting("new_parameter", text))

# 添加参数加载
if "new_parameter" in self.setting:
    self.ui.new_comboBox.setCurrentText(self.setting["new_parameter"])
```
