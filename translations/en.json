{"window_title": "Zhengyin Multispectral Control Software v3.7 20250711", "connect": "Connect", "disconnect": "Disconnect", "connection": "Connection", "start": "Start", "stop": "Stop", "save": "Save", "import": "Import", "export": "Export", "measure": "Measure", "match": "Match", "calibrate": "Calibrate", "clear": "Clear", "send": "SEND", "calibration": "Calibration", "spectral_matching": "  Spectral Matching  ", "save_command": "  Save Command  ", "measurement_500a": "Measurement-500A", "channels": "Channels:", "nodes": "Nodes:", "save_path": "Save Path:", "match_data": "Match Data:", "match_method": "Match Method:", "start_calibration": "Start Calibration", "stop_calibration": "Stop Calibration", "start_matching": "Start Matching", "import_command": "Import Command", "save_command_btn": "Save Command", "connect_success": "Connection Successful", "connect_failed": "Connection Failed", "calibration_complete": "Calibration Complete", "measurement_success": "Measurement Successful", "measurement_failed": "Measurement Failed", "language": "Language", "chinese": "中文", "english": "English", "settings": "Settings", "light_source_1": "Light Source 1", "light_source_2": "Light Source 2", "light_source_3": "Light Source 3", "light_source_4": "Light Source 4", "light_source_5": "Connect Light Source 5 Port:", "light_source_6": "Connect Light Source 6 Port:", "disconnect_port": "Disconnect Port:", "send_data": "Channel Send Data:", "no_device_selected": "No device selected, unable to send data!", "linearization_complete": "Linearization calibration complete, data saved!", "info": "INFO", "error": "ERROR", "warning": "WARNING", "send_msg": "SEND", "tab_calibration": "  Calibration  ", "tab_spectral_matching": "  Spectral Matching  ", "tab_save_command": "  Save Command  ", "tab_range": "  Range  ", "tab_loop": "  Loop  ", "groupbox_connection": "Connection", "groupbox_calibration": "Calibration", "groupbox_measurement_500a": "Measurement-500A", "label_channels": "Channels:", "label_nodes": "Nodes:", "label_save_path": "Save Path:", "label_match_data": "Match Data:", "label_match_method": "Match Method:", "placeholder_channels": "0~13, use English comma", "button_save_range": "Save", "button_run_range": "Run", "button_save_loop": "Save", "button_run_loop": "Run", "button_stop_loop": "Stop", "button_import_loop": "Import", "table_header_number": "Number", "table_header_name": "Name", "table_header_data": "Data", "table_header_select": "Select", "label_light_source": "Light Source:", "label_instrument": "Instrument:", "label_port": "Port:", "label_port_colon": "Port:", "label_channels_colon": "Channels:", "label_nodes_colon": "Nodes:", "label_save_colon": "Save:", "tab_multispectral_source": "Multispectral Source", "button_set_light_source": "Set Light Source", "button_write_to_light_source": "Write to Light Source", "tab_data": " Data ", "tab_spectral_matching_inner": "  Spectral Matching  ", "tab_save_command_inner": "  Save Command  ", "button_view_data": "View Data", "label_match_data_colon": "Match Data:", "button_save_command": "Save Command", "label_match_method_colon": "Match Method:", "label_spectral_accuracy_colon": "Spectral Accuracy:", "label_target_brightness_colon": "Target Brightness (lx/m2):", "label_realtime_control_colon": "Real-time Control:", "button_open": "Open", "button_settings": "Settings", "checkbox_yes": "Yes", "checkbox_no": "No", "unit_nm": "nm", "unit_lux_per_m2": "lx/m2", "tab_multispectral_light_source": "Multispectral Light Source", "label_light_source_colon": "Light Source:", "label_port_colon_simple": "Port:", "tab_scene": "Scene", "tab_flicker": "Flicker", "groupbox_match_data": "Match Data:", "multispectral_light_source": "Multispectral Light Source", "button_reset": "Reset", "table_header_notes": "Notes", "table_header_queue": "Queue", "table_header_delay": "Delay(s)", "table_header_data1": "Data1", "table_header_data2": "Data2", "label_number": "Number:", "label_notes": "Notes:", "label_queue": "Queue:", "label_delay": "Delay(s):", "column_number": "Number", "column_notes": "Notes", "column_queue": "Queue", "column_delay": "Delay(s)", "column_data1": "Data1", "column_data2": "Data2", "column_select": "Select", "msg_channel_send_data": "Channel {0} sending data: {1} [0-1000]", "msg_no_device_selected": "No device selected, unable to send data!", "msg_connect_light_source": "Connect light source {0} serial port: {1}", "msg_connect_light_source_failed": "Connect light source {0} serial port: {1} failed", "msg_disconnect_serial_port": "Disconnect serial port: {1}", "msg_disconnect_serial_port_failed": "Disconnect serial port: {1} failed", "msg_connect_serial_port": "Connect serial port: {1}", "msg_connect_serial_port_failed": "Connect serial port: {1} failed", "msg_cl500a_measurement_complete": "CL500A measurement complete!\nSave path: {0}", "msg_measurement_channel_complete": "Measurement Channel: {0} complete, save path: {1}", "msg_led_data_folders_updated": "LED data folder list updated, found {0} folders", "msg_leddata_folder_not_exist": "ledData folder does not exist, created automatically", "msg_update_led_folders_error": "Error updating LED data folder list: {0}", "msg_channel_count_mismatch": "Channel count mismatch!\nSettings: {0}\nImported: {1}", "msg_import_data_success": "Import data successful! Path: {0}", "msg_match_option_selected": "Match option button group selected: {0}", "msg_connect_serial": "Connect Serial Port: {0}", "msg_connect_failed": "Connection Failed", "msg_connect_success": "Connection Successful", "msg_disconnect": "Disconnect", "msg_measurement_start": "【---------------------【{0} Start Measurement】------------------------】", "msg_measurement_failed": "【---------------------【{0} Measurement Failed】------------------------】", "msg_measurement_complete": "【---------------------【Measurement Complete】------------------------】", "msg_save_location": "Save Location: {0}", "msg_save_complete": "Save Complete!\nSave Command: {0}", "msg_calibration_complete": "Calibration Complete", "msg_calibration_failed": "Calibration Failed", "msg_check_device": "Please Check if {0} is Normal!", "msg_environment_error": "{0} Environment Error!", "msg_collection_complete": "Collection Complete!", "msg_collection_failed": "Collection Failed!\nPlease Check Configuration!"}