#%%
squares = []
for x in range(5):
    squares.append(lambda n=x,: n**2)

print([i() for i in squares])

#%%
a = [1,2,3,4]  + [1,2,3]
#%%
set(a)
#%%
a = {1,2,3}
#%%
b = {1,2}
#%%
len(a-b)
#%%
len(b-a)
#%%
port_list_name = {}
#%%
port_list_name.__str__
#%%
a = ["1",2,3]
#%%
b = set(a)
#%%
b
#%%
for i in b:
    print(i)
#%%
ORDER1 = "00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 29 0d 0d 0a"
ORDER2 = "01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 29 0d 0d 0a"
ORDER3 = "02 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 29 0d 0d 0a"

#%%
import json
STDDATA = {'A': [0.0,
  0.0,
  0.0,
  0.0,
  0.009603847,
  0.010686505,
  0.011848821,
  0.013092634,
  0.014419544,
  0.015830908,
  0.017327825,
  0.018911143,
  0.020581449,
  0.022339073,
  0.024184087,
  0.026116308,
  0.028135304,
  0.030240399,
  0.03243068,
  0.034705005,
  0.03706201,
  0.039500121,
  0.042017567,
  0.044612383,
  0.047282432,
  0.050025407,
  0.05283885,
  0.055720161,
  0.058666614,
  0.061675365,
  0.064743466,
  0.067867878,
  0.071045485,
  0.074273099,
  0.077547481,
  0.080865344,
  0.084223367,
  0.087618206,
  0.091046504,
  0.094504898,
  0.097990031,
  0.101498559,
  0.105027157,
  0.108572532,
  0.112131425,
  0.115700619,
  0.119276946,
  0.122857293,
  0.126438604,
  0.130017887,
  0.133592219,
  0.137158749,
  0.140714699,
  0.144257371,
  0.147784147,
  0.151292491,
  0.154779952,
  0.158244166,
  0.161682855,
  0.16509383,
  0.168474991,
  0.171824326,
  0.175139913,
  0.17841992,
  0.181662602,
  0.184866304,
  0.188029457,
  0.19115058,
  0.194228277,
  0.197261236,
  0.200248228,
  0.203188107,
  0.206079806,
  0.208922335,
  0.211714785,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0],
 'D50': [0.0,
  0.0,
  0.0,
  0.0,
  0.032589153,
  0.036173756,
  0.039758359,
  0.052696975,
  0.065635591,
  0.070435112,
  0.075234634,
  0.077581729,
  0.079928825,
  0.078455859,
  0.076982894,
  0.088310946,
  0.099638999,
  0.107915198,
  0.116191397,
  0.118435933,
  0.120680469,
  0.121187938,
  0.121695407,
  0.124190024,
  0.126684641,
  0.124593708,
  0.122502775,
  0.125012063,
  0.127521352,
  0.128116682,
  0.128712012,
  0.129059132,
  0.129406252,
  0.132719858,
  0.136033464,
  0.135140336,
  0.134247209,
  0.135290224,
  0.13633324,
  0.13479163,
  0.13325002,
  0.131742596,
  0.130235172,
  0.131025437,
  0.131815702,
  0.128209063,
  0.124602424,
  0.127397674,
  0.130192925,
  0.131249664,
  0.132306402,
  0.132157229,
  0.132008056,
  0.129797925,
  0.127587795,
  0.12968166,
  0.131775526,
  0.129651509,
  0.127527492,
  0.129212276,
  0.13089706,
  0.13410848,
  0.1373199,
  0.134741935,
  0.132163971,
  0.124328353,
  0.116492735,
  0.119310144,
  0.122127553,
  0.122980782,
  0.12383401,
  0.113145917,
  0.102457825,
  0.108893848,
  0.115329872,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0],
 'D55': [0.0,
  0.0,
  0.0,
  0.0,
  0.043218115,
  0.046873126,
  0.050528138,
  0.065698476,
  0.080868814,
  0.085917411,
  0.090966009,
  0.092975707,
  0.094985405,
  0.092558054,
  0.090130702,
  0.101877,
  0.113623297,
  0.121849714,
  0.130076131,
  0.131719981,
  0.133363832,
  0.133003046,
  0.132642261,
  0.134522152,
  0.136402042,
  0.133311623,
  0.130221204,
  0.131953195,
  0.133685187,
  0.133698428,
  0.133711669,
  0.133245231,
  0.132778794,
  0.135585708,
  0.138392623,
  0.136995632,
  0.135598641,
  0.136176061,
  0.13675348,
  0.134784766,
  0.132816053,
  0.130969289,
  0.129122525,
  0.129478093,
  0.129833661,
  0.125641581,
  0.121449502,
  0.123437532,
  0.125425562,
  0.125907041,
  0.12638852,
  0.125779987,
  0.125171453,
  0.122667573,
  0.120163693,
  0.121417653,
  0.122671612,
  0.120364267,
  0.118056923,
  0.119031955,
  0.120006987,
  0.122423057,
  0.124839127,
  0.122187662,
  0.119536197,
  0.112704858,
  0.105873519,
  0.107977488,
  0.110081457,
  0.111409872,
  0.112738287,
  0.103032417,
  0.093326548,
  0.099349165,
  0.105371781,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0],
 'D65': [0.0,
  0.0,
  0.0,
  0.0,
  0.064058687,
  0.06705862,
  0.070058554,
  0.088081817,
  0.10610508,
  0.111704171,
  0.117303262,
  0.11855354,
  0.119803818,
  0.115479392,
  0.111154965,
  0.122819507,
  0.134484049,
  0.142274595,
  0.150065142,
  0.150584202,
  0.151103262,
  0.14921402,
  0.147324779,
  0.148009185,
  0.148693591,
  0.144134811,
  0.13957603,
  0.13992838,
  0.14028073,
  0.139287217,
  0.138293704,
  0.136365456,
  0.134437208,
  0.136299708,
  0.138162208,
  0.136057252,
  0.133952295,
  0.133723855,
  0.133495414,
  0.130901392,
  0.128307369,
  0.125957085,
  0.123606801,
  0.123258158,
  0.122909514,
  0.118353759,
  0.113798004,
  0.114647674,
  0.115497343,
  0.115237768,
  0.114978194,
  0.113760481,
  0.112542769,
  0.109714475,
  0.106886181,
  0.107151496,
  0.107416811,
  0.10506092,
  0.10270503,
  0.102827194,
  0.102949358,
  0.104275218,
  0.105601078,
  0.103038867,
  0.100476656,
  0.094981038,
  0.089485419,
  0.090698196,
  0.091910972,
  0.093667897,
  0.095424821,
  0.087245804,
  0.079066787,
  0.084380859,
  0.089694931,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0],
 'D75': [0.0,
  0.0,
  0.0,
  0.0,
  0.081533772,
  0.083531099,
  0.085528426,
  0.105073808,
  0.124619191,
  0.130711728,
  0.136804264,
  0.137359295,
  0.137914327,
  0.131983303,
  0.126052278,
  0.137127542,
  0.148202806,
  0.155428588,
  0.16265437,
  0.162256902,
  0.161859434,
  0.158784665,
  0.155709896,
  0.155393731,
  0.155077567,
  0.149565784,
  0.144054002,
  0.143327372,
  0.142600743,
  0.140837455,
  0.139074168,
  0.135993496,
  0.132912825,
  0.134007152,
  0.135101479,
  0.13256134,
  0.1300212,
  0.129175734,
  0.128330267,
  0.125332586,
  0.122334905,
  0.119654724,
  0.116974543,
  0.116117723,
  0.115260903,
  0.110847131,
  0.106433359,
  0.106576399,
  0.106719438,
  0.106055806,
  0.105392173,
  0.103828142,
  0.10226411,
  0.09930777,
  0.09635143,
  0.096157595,
  0.095963759,
  0.093745069,
  0.091526378,
  0.091236104,
  0.090945829,
  0.09161924,
  0.092292651,
  0.089939821,
  0.087586991,
  0.082860941,
  0.078134891,
  0.078884463,
  0.079634035,
  0.081464992,
  0.083295948,
  0.076182248,
  0.069068547,
  0.073839893,
  0.078611239,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0,
  0.0]}
#%%
test_dict = {'0':"00 00 00",
'1':"01 00 00",
'2':"02 00 00",
}
my_dict = {}
my_dict["channels_num"] = 20
my_dict["protocol"] = test_dict
my_dict["std_data"] = STDDATA

#dumps 将数据转换成字符串
# json_str = json.dumps(test_dict)
with open("setting.json","w",encoding="utf-8") as f:
     json.dump(my_dict,f,indent=4, ensure_ascii=False)
     print("加载入文件完成...")
#%%
import time
time.strftime("%Y-%m-%d %H:%M:%S",time.gmtime())
#%%
import numpy as np
#%%
a = np.arange(9).reshape(3,3)
#%%
np.max(a,0)
#%%
import random
color = lambda : random.randint(0,255)
my_color = (color(),color(),color())
print(my_color)
#%%
import matplotlib.pyplot as plt
#%%
plt.plot([1,2],[1,2],color=(0.2,0,0))
plt.show()
#%%
a = np.arange(9).reshape(3,3)
#%%
b = a*[1,2,3]
b
#%%
np.sum(b,axis=1)
#%%

#%%
x = np.linspace(360,780,421)
#%%
x
#%%
import numpy as np
#%%
a = [True,True,False,True,True,False]
b = [1,2,4,5]

#%%
index = np.where([not x for x in a])[0]
index
#%%
for i in index:
    b.insert(i,i+1)
#%%
b
#%%
import numpy as np
#%%
0.5**(np.log(0.18)/np.log(0.5))
#%%
1/2.47
#%%
np.log(0.7)/np.log(x) = np.log(0.18)/np.log(0.5)
#%%
a = 1/(np.log(0.18)/np.log(0.5)/np.log(0.7))
#%%
np.e**a * 255
#%% md
# 350-1100补零
#%%
import os
import numpy as np
#%%
pathDir = r"C:\Users\<USER>\Desktop\ColorSpace_Multispectral_XM\ColorSpace_Multispectral_XM\ledData\凌云宏普校准 29"
names = os.listdir(pathDir)
paths = [os.path.join(pathDir, name) for name in names]
saveDir = r"C:\Users\<USER>\Desktop\ColorSpace_Multispectral_XM\ColorSpace_Multispectral_XM\ledData\凌云宏普校准 29_补零"
os.makedirs(saveDir, exist_ok=True)
for path in paths:
    data = np.loadtxt(path,delimiter=",")
    # 获取到mxn的矩阵，在第二行插入10行，全0
    data = np.insert(data,1,np.zeros((10,data.shape[1])),axis=0)
    # 在尾部插入1100-780 = 320行，全0
    data = np.insert(data,data.shape[0],np.zeros((320,data.shape[1])),axis=0)
    np.savetxt(os.path.join(saveDir,os.path.basename(path)),data,delimiter=",")
#%%
path =  r"C:\Users\<USER>\Desktop\ColorSpace_Multispectral_XM\ColorSpace_Multispectral_XM\ledData\凌云宏普校准 29_补零\02.csv"
data = np.loadtxt(path,delimiter=",")
clip = data[1:,0]
np.savetxt(r"C:\Users\<USER>\Desktop\ColorSpace_Multispectral_XM\ColorSpace_Multispectral_XM\350_1100_test.csv",clip,delimiter=",")
#%%
