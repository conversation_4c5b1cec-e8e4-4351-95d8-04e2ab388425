#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller 构建脚本
用于解决依赖问题并创建可执行文件
"""

import os
import sys
import subprocess

def create_spec_file():
    """创建 PyInstaller 规格文件"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 添加隐藏导入
hiddenimports = [
    'scipy',
    'scipy.interpolate',
    'scipy.interpolate.interpolate',
    'scipy.interpolate._interpolate',
    'scipy.special',
    'scipy.special._ufuncs_cxx',
    'scipy.linalg',
    'scipy.linalg.cython_blas',
    'scipy.linalg.cython_lapack',
    'scipy.sparse',
    'scipy.sparse.csgraph',
    'scipy.sparse.csgraph._validation',
    'scipy.ndimage',
    'numpy',
    'numpy.core',
    'numpy.core._multiarray_umath',
    'numpy.random',
    'numpy.random._pickle',
    'numpy.random._common',
    'numpy.random._bounded_integers',
    'numpy.random._mt19937',
    'numpy.random._philox',
    'numpy.random._pcg64',
    'numpy.random._sfc64',
    'numpy.random._generator',
    'numpy.random.bit_generator',
    'numpy.random.mtrand',
    'matplotlib',
    'matplotlib.pyplot',
    'matplotlib.backends',
    'matplotlib.backends.backend_qt5agg',
    'matplotlib.backends.backend_tkagg',
    'pyqtgraph',
    'pyqtgraph.graphicsItems',
    'pyqtgraph.widgets',
    'serial',
    'serial.tools',
    'serial.tools.list_ports',
    'PyQt5',
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'PyQt5.uic',
    'ctypes',
    'ctypes.util',
    'json',
    'csv',
    'threading',
    'time',
    'os',
    'sys',
    'inspect',
]

# 数据文件
datas = [
    ('translations', 'translations'),
    ('*.ui', '.'),
    ('*.qrc', '.'),
    ('icons', 'icons'),
    ('ledData', 'ledData'),
    ('tempData', 'tempData'),
]

# 二进制文件
binaries = []

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ColorSpace_Multispectral',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 设置为 False 隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''
    
    with open('main.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("已创建 main.spec 文件")

def install_dependencies():
    """安装必要的依赖"""
    dependencies = [
        'pyinstaller',
        'scipy',
        'numpy',
        'matplotlib',
        'pyqtgraph',
        'pyserial',
        'PyQt5',
    ]
    
    for dep in dependencies:
        try:
            print(f"检查依赖: {dep}")
            subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                         check=True, capture_output=True)
            print(f"✓ {dep} 已安装")
        except subprocess.CalledProcessError as e:
            print(f"✗ 安装 {dep} 失败: {e}")

def build_executable():
    """构建可执行文件"""
    try:
        print("开始构建可执行文件...")
        
        # 使用 spec 文件构建
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller', 
            '--clean',  # 清理缓存
            'main.spec'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 构建成功!")
            print("可执行文件位置: dist/ColorSpace_Multispectral.exe")
        else:
            print("✗ 构建失败:")
            print(result.stderr)
            
    except Exception as e:
        print(f"构建过程中出现错误: {e}")

if __name__ == "__main__":
    print("=== PyInstaller 构建工具 ===")
    
    # 1. 安装依赖
    print("\n1. 检查并安装依赖...")
    install_dependencies()
    
    # 2. 创建规格文件
    print("\n2. 创建 PyInstaller 规格文件...")
    create_spec_file()
    
    # 3. 构建可执行文件
    print("\n3. 构建可执行文件...")
    build_executable()
    
    print("\n=== 构建完成 ===")
