import os
import csv
import time


# if __name__ == '__main__':
#    # print(getPhoto(r'C:\Users\<USER>\Desktop\IMAGE'))
#
#     for i in range(100000):
#         print(os.system('Measurement.exe'))
#
#         data = []
#         data.clear()
#         with open("Measurement.csv", mode="r", encoding="utf-8-sig") as rf:
#             reader = csv.reader(rf)
#             for item in reader:
#                 print(item)
#                 data.append(item)
#
#         print('Ev:',str(data[4][1]))
#
#         with open(r"./test.txt", 'a+') as f:
#             f.write(str(data[4][1]))
#             f.write(' ')
#             f.write(str(data[9][1]))
#
#             f.write('\n')
#
#         #time.sleep(10)

list =[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0007457742933183909, 0.0009775381535291673, 0.000976966880261898, 0.0009835421107709408, 0.00019025811925530434, 5.396171036409214e-06, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2249436986166984e-05, 1.2001618597423657e-05, 1.1806378461187706e-05, 1.1612512025749311e-05, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.00027898442931473255, 0.0007608951069414616, 0.002431994862854481, 0.004711154103279114, 0.008280432224273682, 0.01263578087091446, 0.017234203219413758, 0.023571635782718658, 0.029796940088272095, 0.03831479847431183, 0.04851987361907959, 0.0580257773399353, 0.06771425604820251, 0.07885759472846984, 0.09313862323760987, 0.11081475019454956, 0.13058305978775026, 0.1522632956504822, 0.17365394830703734, 0.19631091356277466, 0.22474358081817628, 0.2555641412734985, 0.2888296127319336, 0.3209099292755127, 0.3562185287475586, 0.40012311935424805, 0.44858741760253906, 0.5011134624481202, 0.550840187072754, 0.6039470195770263, 0.6665626049041748, 0.7322940826416016, 0.7998500823974609, 0.8600301742553711, 0.9187666893005371, 0.9824785232543946, 1.041853141784668, 1.094625186920166, 1.1364154815673828, 1.1705522537231445, 1.1976298332214355, 1.2117243766784669, 1.2112745285034179, 1.20103178024292, 1.181925106048584, 1.1523606300354003, 1.1143094062805177, 1.068746280670166, 1.0244190216064453, 0.9744152069091797, 0.9157876014709473, 0.8584685325622559, 0.8045822143554687, 0.7613019466400146, 0.7211214065551758, 0.6788334846496582, 0.6403329372406006, 0.6063008785247803, 0.5795587539672852, 0.5543759346008301, 0.5263057231903077, 0.4995882034301758, 0.4746415138244629, 0.45450410842895506, 0.4361306667327881, 0.41642074584960936, 0.3993906259536743, 0.38735997676849365, 0.3773683786392212, 0.36786062717437745, 0.3607389688491821, 0.35620644092559817, 0.35463848114013674, 0.3547773599624634, 0.3559454917907715, 0.35827088356018066, 0.3616809606552124, 0.36623172760009765, 0.37206497192382815, 0.37936739921569823, 0.3869655609130859, 0.39426751136779786, 0.40228872299194335, 0.4112076759338379, 0.4207793712615967, 0.4300680637359619, 0.4390717506408691, 0.4486690521240234, 0.45832390785217286, 0.4672891616821289, 0.47637219429016114, 0.4858811378479004, 0.4953951358795166, 0.504647970199585, 0.5127094745635986, 0.5206017017364502, 0.5282990455627441, 0.5356649875640869, 0.5424438953399658, 0.548514986038208, 0.5547813892364502, 0.5602713108062745, 0.5652158260345459, 0.570098876953125, 0.5739955902099609, 0.5776808261871338, 0.5812841415405273, 0.5843832492828369, 0.5868852138519287, 0.5887443542480468, 0.5910949230194091, 0.5938727378845214, 0.5957976818084717, 0.5974873542785645, 0.5987520694732666, 0.5993274688720703, 0.599956750869751, 0.6004521369934082, 0.601552677154541, 0.6028494834899902, 0.6032707214355468, 0.6036089420318603, 0.6044894218444824, 0.6055202960968018, 0.6065011024475098, 0.6076968669891357, 0.6091514110565186, 0.6102546691894531, 0.6107142448425293, 0.611426305770874, 0.6130115509033203, 0.6144356727600098, 0.6152220249176026, 0.6157288551330566, 0.6164713382720948, 0.6174842834472656, 0.6177757263183594, 0.6179737091064453, 0.6187693119049072, 0.6195127964019775, 0.6203685283660889, 0.6213526248931884, 0.6225222587585449, 0.6235282897949219, 0.6237768650054931, 0.6243852615356446, 0.6250053882598877, 0.6251217365264893, 0.6251357078552247, 0.6255719661712646, 0.626220417022705, 0.6261292457580566, 0.6259358406066895, 0.6263617515563965, 0.6264869213104248, 0.6262545108795166, 0.6265278816223144, 0.626509141921997, 0.6258087158203125, 0.6250279903411865, 0.6247335910797119, 0.6243155002593994, 0.6232877254486084, 0.6219354152679444, 0.6209841728210449, 0.620078182220459, 0.6185887336730957, 0.617573356628418, 0.6161791801452636, 0.6137077331542968, 0.6107865810394287, 0.6087990283966065, 0.6069758415222168, 0.6045130252838135, 0.6027729988098145, 0.6005923748016357, 0.5975059032440185, 0.5941596508026123, 0.5913191795349121, 0.5883782386779786, 0.5842114448547363, 0.5805660247802734, 0.5772942543029785, 0.5733749866485596, 0.5693619728088379, 0.5660888195037842, 0.5619357109069825, 0.5565348148345948, 0.5519776821136475, 0.5475531101226807, 0.5425215721130371, 0.5371956825256348, 0.532868480682373, 0.5282963275909424, 0.5223590850830078, 0.5168972969055176, 0.5110833168029785, 0.5045003414154052, 0.4978975296020508, 0.4926771640777588, 0.48745107650756836, 0.48044981956481936, 0.4744412899017334, 0.4682363510131836, 0.46055045127868655, 0.4541297912597656, 0.4475089550018311, 0.43962483406066893, 0.43194870948791503, 0.42549686431884765, 0.419138240814209, 0.4116398334503174, 0.405302095413208, 0.3989128589630127, 0.3912698745727539, 0.38476459980010985, 0.37799246311187745, 0.3701986312866211, 0.36376056671142576, 0.35727903842926023, 0.3498042106628418, 0.34208629131317136, 0.33504295349121094, 0.3280088186264038, 0.31983969211578367, 0.313210654258728, 0.30701327323913574, 0.2998188495635986, 0.2937584638595581, 0.28729820251464844, 0.27968027591705324, 0.2735265254974365, 0.2675351619720459, 0.26099886894226076, 0.2554852247238159, 0.24982821941375732, 0.2430877923965454, 0.23608181476593018, 0.23026905059814454, 0.22447314262390136, 0.21801533699035644, 0.21288981437683105, 0.20772027969360352, 0.2015805721282959, 0.1963937759399414, 0.19109537601470947, 0.18516544103622437, 0.18020635843276978, 0.1755839467048645, 0.1702006697654724, 0.16560580730438232, 0.1611981749534607, 0.1561233162879944, 0.1518869161605835, 0.14788358211517333, 0.14337079524993895, 0.1393693208694458, 0.13529444932937623, 0.1305821180343628, 0.12662997245788574, 0.12275886535644531, 0.11856441497802735, 0.11512103080749511, 0.11182397603988647, 0.10788711309432983, 0.10444318056106568, 0.10131077766418457, 0.09761180877685546, 0.09443033933639526, 0.09172903895378112, 0.08856878280639649, 0.08582744598388672, 0.08323811292648316, 0.08003110885620117, 0.07750595211982728, 0.07542192935943604, 0.07295273542404175, 0.07143326997756957, 0.06935887932777404, 0.06611843705177307, 0.06350948810577392, 0.06073812842369079, 0.058090460300445554, 0.056433755159378055, 0.055095899105072024, 0.053114020824432374, 0.051277059316635135, 0.04925072491168976, 0.04673458337783813, 0.04472243785858154, 0.04286360442638397, 0.040962117910385135, 0.0394503265619278, 0.03841608762741089, 0.0373548686504364, 0.036311739683151247, 0.03503382503986359, 0.03363223671913147, 0.032301709055900574, 0.030614367127418517, 0.029312986135482787, 0.027897316217422485, 0.026367205381393432, 0.025278660655021667, 0.024254466593265533, 0.023477229475975036, 0.022956885397434235, 0.022068481147289275, 0.020984363555908204, 0.01994956284761429, 0.018961130082607268, 0.01768566817045212, 0.016404363512992858, 0.014955054223537444, 0.013585740327835083, 0.01274576485157013, 0.012258287519216537, 0.01210896372795105, 0.011866045743227005, 0.011515380442142486, 0.011026321351528168, 0.010338100790977477, 0.009721517562866211, 0.00907839834690094, 0.008626242727041244, 0.008155088871717453, 0.007716261595487594, 0.007109062373638153, 0.0068946793675422665, 0.006285784393548965, 0.0052606664597988125, 0.004489469900727272, 0.003728107735514641, 0.0031917136162519454, 0.0034288477152585985, 0.0036448556929826736, 0.0034265745431184768, 0.003044549189507961, 0.0026016538962721826, 0.0018730245530605317, 0.001386985182762146, 0.0016194000840187074, 0.0020047295838594437, 0.0019951134920120238, 0.0018207058310508728, 0.001425774022936821, 0.000761214131489396, 0.0004520848393440247, 0.00043807937763631346, 0.00043328949250280856, 0.00013378207804635168, 1.692203077254817e-05, 2.8690131148323417e-05, 2.901744155678898e-05, 0.00014894602354615928, 0.00032829332631081345, 0.00031374250538647176, 0.0003274438437074423, 0.0002659340854734182, 2.4375430075451732e-05, 1.814354909583926e-05, 9.123404743149876e-06, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.027473086258397e-08, 1.2158205890955288e-07, 1.2268255886738188e-07, 1.2375141977827298e-07, 6.243449774956389e-08, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.133504161378369e-06, 1.2308088480494917e-05, 1.2418917322065681e-05, 1.252613728865981e-05, 8.424453699262813e-06, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0014774199575185777, 0.0018888600170612335, 0.0019120311364531517, 0.0019344186410307884, 0.0008988340385258197, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.00021001312416046858, 0.00021302392706274987, 0.00021589826792478561, 0.00021871512290090324, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.00014212221140041947, 0.00014415032928809522, 0.00014639125438407064, 0.00014881351962685584, 5.041637341491878e-05, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.001714736595749855, 0.002698539011180401, 0.0027738431468605994, 0.00285087488591671, 0.0017533600330352783, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.001700354740023613, 0.01931026726961136, 0.020320712029933928, 0.021014828979969025, 0.02160024791955948, 0.004611564800143242, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]

print(list[97])