#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试生成的可执行文件
"""

import os
import subprocess
import time

def test_executable():
    """测试可执行文件"""
    exe_path = os.path.join("dist", "ColorSpace_Multispectral.exe")
    
    if not os.path.exists(exe_path):
        print("❌ 可执行文件不存在!")
        return False
    
    print(f"✓ 找到可执行文件: {exe_path}")
    
    # 获取文件大小
    file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
    print(f"✓ 文件大小: {file_size:.1f} MB")
    
    print("\n测试启动可执行文件...")
    print("注意: 程序将启动，请手动关闭以完成测试")
    
    try:
        # 启动程序（非阻塞）
        process = subprocess.Popen([exe_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # 等待几秒钟看是否有错误
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✓ 程序成功启动并正在运行")
            print("请手动测试程序功能，然后关闭程序")
            return True
        else:
            # 进程已经结束，可能有错误
            stdout, stderr = process.communicate()
            print("❌ 程序启动失败")
            if stderr:
                print("错误信息:")
                print(stderr.decode('utf-8', errors='ignore'))
            return False
            
    except Exception as e:
        print(f"❌ 启动程序时出错: {e}")
        return False

def main():
    """主函数"""
    print("=== 可执行文件测试 ===")
    print()
    
    success = test_executable()
    
    print()
    if success:
        print("=== 测试完成 ===")
        print("✓ 可执行文件构建成功并能正常启动")
        print("✓ 位置: dist\\ColorSpace_Multispectral.exe")
        print()
        print("使用说明:")
        print("1. 可执行文件已经包含了所有必要的依赖")
        print("2. 可以直接在没有Python环境的电脑上运行")
        print("3. 确保目标电脑有足够的内存和存储空间")
        print("4. 如果遇到杀毒软件误报，请添加信任")
    else:
        print("=== 测试失败 ===")
        print("❌ 可执行文件可能存在问题")
        print("请检查构建过程中的错误信息")

if __name__ == "__main__":
    main()
