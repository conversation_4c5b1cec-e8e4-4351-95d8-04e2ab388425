# 标签翻译功能更新说明

## 问题描述
用户反馈在点击语言切换按钮时，界面中的一些标签文本（如"光源:"、"仪器:"、"端口:"、"通道:"、"节点:"、"保存:"等）没有从中文切换到英文显示。

## 解决方案

### 1. 扩展翻译文件
在 `translations/zh.json` 和 `translations/en.json` 中添加了以下标签的翻译：

#### 新增的中文翻译
```json
{
  "label_light_source": "光源：",
  "label_instrument": "仪器：", 
  "label_port": "端口:",
  "label_port_colon": "端口：",
  "label_channels_colon": "通道:",
  "label_nodes_colon": "节点:",
  "label_save_colon": "保存:",
  "tab_multispectral_source": "多光谱光源",
  "button_set_light_source": "设置光源",
  "button_write_to_light_source": "写入光源"
}
```

#### 新增的英文翻译
```json
{
  "label_light_source": "Light Source:",
  "label_instrument": "Instrument:",
  "label_port": "Port:",
  "label_port_colon": "Port:",
  "label_channels_colon": "Channels:",
  "label_nodes_colon": "Nodes:",
  "label_save_colon": "Save:",
  "tab_multispectral_source": "Multispectral Source",
  "button_set_light_source": "Set Light Source",
  "button_write_to_light_source": "Write to Light Source"
}
```

### 2. 更新主程序代码

#### 增强 `update_label_texts()` 方法
```python
def update_label_texts(self):
    """更新标签文本"""
    # 更新标签文本
    label_mappings = {
        'label': 'label_light_source',        # 第一个光源：
        'label_2': 'label_light_source',      # 第二个光源：
        'label_3': 'label_light_source',      # 第三个光源：
        'label_4': 'label_light_source',      # 第四个光源：
        'label_5': 'label_instrument',        # 仪器：
        'label_6': 'label_port_colon',        # 端口：
        'label_23': 'label_channels_colon',   # 通道:
        'label_28': 'label_nodes_colon',      # 节点:
        'label_29': 'label_save_colon'        # 保存:
    }
    
    for label_name, text_key in label_mappings.items():
        if hasattr(self.ui, label_name):
            label = getattr(self.ui, label_name)
            label.setText(language_manager.get_text(text_key))
```

#### 添加 `update_tab_titles()` 方法
```python
def update_tab_titles(self):
    """更新标签页标题"""
    # 更新主标签页
    if hasattr(self.ui, 'tabWidget_2'):
        tab_widget = self.ui.tabWidget_2
        # 更新各个标签页的标题
        for i in range(tab_widget.count()):
            if tab_widget.tabText(i) in ["多光谱光源", "Multispectral Source"]:
                tab_widget.setTabText(i, language_manager.get_text('tab_multispectral_source'))
            # ... 其他标签页
```

#### 更新 `apply_language_settings()` 方法
在语言切换时调用所有更新方法：
```python
def apply_language_settings(self):
    """应用语言设置到UI元素"""
    # 更新窗口标题
    self.ui.setWindowTitle(language_manager.get_text("window_title"))
    
    # 更新按钮文本
    self.update_button_texts()
    
    # 更新标签文本
    self.update_label_texts()
    
    # 更新组框标题
    self.update_groupbox_texts()
    
    # 更新标签页标题
    self.update_tab_titles()
```

### 3. 标签名称映射

通过分析UI文件 `ui/mainwindow.ui`，找到了各个标签的实际名称：

| 显示文本 | UI标签名称 | 翻译键值 |
|---------|-----------|---------|
| 光源： | label, label_2, label_3, label_4 | label_light_source |
| 仪器： | label_5 | label_instrument |
| 端口： | label_6 | label_port_colon |
| 通道: | label_23 | label_channels_colon |
| 节点: | label_28 | label_nodes_colon |
| 保存: | label_29 | label_save_colon |

### 4. 测试验证

创建了测试脚本 `test_label_translation.py` 来验证翻译功能：

```bash
python test_label_translation.py
```

测试结果显示所有标签都能正确切换：
- 中文：光源：、仪器：、端口：、通道:、节点:、保存:
- 英文：Light Source:、Instrument:、Port:、Channels:、Nodes:、Save:

## 功能效果

### 修复前
- 点击语言切换按钮时，标签文本保持中文不变
- 只有部分按钮和菜单文本会切换

### 修复后  
- 点击语言切换按钮时，所有标签文本都会相应切换
- 包括：光源标签、仪器标签、端口标签、通道标签、节点标签、保存标签
- 按钮文本也会同步切换：设置光源 ↔ Set Light Source

## 使用方法

1. 启动软件：`python main.py`
2. 点击菜单栏的"语言"菜单
3. 选择"中文"或"English"
4. 界面中的所有文本（包括标签）都会立即切换到对应语言

## 技术要点

1. **UI元素识别**：通过分析UI文件找到标签的实际名称
2. **动态更新**：使用 `setText()` 方法动态更新标签文本
3. **翻译映射**：建立UI元素名称与翻译键值的映射关系
4. **统一调用**：在语言切换时统一调用所有更新方法

## 扩展性

该解决方案具有良好的扩展性：
- 可以轻松添加更多标签的翻译支持
- 可以添加更多语言（如日语、韩语等）
- 翻译文件采用JSON格式，易于维护和修改

## 版本信息

- 更新日期：2025-07-30
- 修复版本：v3.7+
- 兼容性：PyQt5, Windows系统
