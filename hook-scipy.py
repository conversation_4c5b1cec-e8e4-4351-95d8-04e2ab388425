# PyInstaller hook for scipy
from PyInstaller.utils.hooks import collect_submodules, collect_data_files

# 收集所有 scipy 子模块
hiddenimports = collect_submodules('scipy')

# 添加特定的隐藏导入
hiddenimports += [
    'scipy.interpolate',
    'scipy.interpolate.interpolate',
    'scipy.interpolate._interpolate', 
    'scipy.interpolate._cubic',
    'scipy.interpolate._bsplines',
    'scipy.interpolate._fitpack',
    'scipy.interpolate._fitpack2',
    'scipy.interpolate.fitpack',
    'scipy.interpolate.fitpack2',
    'scipy.interpolate.dfitpack',
    'scipy.interpolate._ppoly',
    'scipy.interpolate.polyint',
    'scipy.interpolate.rbf',
    'scipy.interpolate.ndgriddata',
    'scipy.special',
    'scipy.special._ufuncs',
    'scipy.special._ufuncs_cxx',
    'scipy.special._ellip_harm_2',
    'scipy.special._basic',
    'scipy.special._logsumexp',
    'scipy.special._orthogonal',
    'scipy.special._spherical_bessel',
    'scipy.special._lambertw',
    'scipy.special._ellipk',
    'scipy.special._ellipkinc',
    'scipy.special._ellipe',
    'scipy.special._ellipeinc',
    'scipy.linalg',
    'scipy.linalg.cython_blas',
    'scipy.linalg.cython_lapack',
    'scipy.linalg._fblas',
    'scipy.linalg._flapack',
    'scipy.linalg.lapack',
    'scipy.linalg.blas',
    'scipy.sparse',
    'scipy.sparse.linalg',
    'scipy.sparse.csgraph',
    'scipy.sparse.csgraph._validation',
    'scipy.ndimage',
    'scipy.ndimage._ni_support',
    'scipy.ndimage._nd_image',
    'scipy.optimize',
    'scipy.integrate',
    'scipy.stats',
    'scipy.signal',
    'scipy.fft',
    'scipy.spatial',
]

# 收集数据文件
datas = collect_data_files('scipy')
